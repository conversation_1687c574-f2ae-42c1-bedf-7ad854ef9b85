<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT解析测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .slides-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .slide {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .slide img {
            width: 100%;
            height: auto;
            border-radius: 4px;
        }
        .slide-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>PPT解析测试工具</h1>
    
    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <p>点击此处或拖拽PPT文件到这里</p>
        <p>支持 .ppt 和 .pptx 格式</p>
        <input type="file" id="fileInput" accept=".ppt,.pptx" style="display: none;">
    </div>
    
    <div class="progress" id="progressContainer" style="display: none;">
        <div class="progress-bar" id="progressBar"></div>
    </div>
    
    <div id="status"></div>
    
    <div class="slides-container" id="slidesContainer"></div>

    <script type="module">
        import JSZip from 'https://cdn.skypack.dev/jszip';
        
        const fileInput = document.getElementById('fileInput');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const status = document.getElementById('status');
        const slidesContainer = document.getElementById('slidesContainer');
        
        // 显示状态信息
        function showStatus(message, type = 'info') {
            status.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        // 更新进度条
        function updateProgress(percent) {
            progressContainer.style.display = 'block';
            progressBar.style.width = percent + '%';
        }
        
        // 隐藏进度条
        function hideProgress() {
            progressContainer.style.display = 'none';
        }
        
        // 解析PPTX文件
        async function parsePPTXFile(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                
                reader.onload = async (e) => {
                    try {
                        showStatus('正在解析PPT文件...', 'info');
                        updateProgress(20);
                        
                        const zip = new JSZip();
                        const zipContent = await zip.loadAsync(e.target.result);
                        
                        updateProgress(40);
                        
                        // 获取幻灯片信息
                        const slideFiles = [];
                        
                        // 查找所有幻灯片文件
                        zipContent.forEach((relativePath, file) => {
                            if (relativePath.startsWith('ppt/slides/slide') && relativePath.endsWith('.xml')) {
                                slideFiles.push({ path: relativePath, file });
                            }
                        });
                        
                        // 按幻灯片编号排序
                        slideFiles.sort((a, b) => {
                            const aNum = parseInt(a.path.match(/slide(\d+)\.xml/)[1]);
                            const bNum = parseInt(b.path.match(/slide(\d+)\.xml/)[1]);
                            return aNum - bNum;
                        });
                        
                        updateProgress(60);
                        showStatus(`找到 ${slideFiles.length} 张幻灯片，正在生成预览...`, 'info');
                        
                        const images = [];
                        
                        // 处理每张幻灯片
                        for (let i = 0; i < slideFiles.length; i++) {
                            const slideFile = slideFiles[i];
                            const slideContent = await slideFile.file.async('text');
                            
                            // 解析幻灯片内容并生成图片
                            const slideImage = await generateSlideImage(slideContent, i + 1, file.name);
                            images.push(slideImage);
                            
                            // 更新进度
                            const progress = 60 + (i + 1) / slideFiles.length * 30;
                            updateProgress(progress);
                        }
                        
                        updateProgress(100);
                        resolve(images);
                        
                    } catch (error) {
                        console.error('PPTX解析失败:', error);
                        reject(error);
                    }
                };
                
                reader.onerror = () => {
                    reject(new Error('文件读取失败'));
                };
                
                reader.readAsArrayBuffer(file);
            });
        }
        
        // 从幻灯片XML内容生成图片
        async function generateSlideImage(slideXML, slideNumber, fileName) {
            const canvas = document.createElement('canvas');
            canvas.width = 1024;
            canvas.height = 768;
            const ctx = canvas.getContext('2d');
            
            // 设置背景
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, 1024, 768);
            
            // 添加边框
            ctx.strokeStyle = '#e0e0e0';
            ctx.lineWidth = 2;
            ctx.strokeRect(0, 0, 1024, 768);
            
            try {
                // 解析XML内容，提取文本
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(slideXML, 'text/xml');
                
                // 查找所有文本内容
                const textElements = xmlDoc.getElementsByTagName('a:t');
                let yPosition = 100;
                
                // 绘制标题
                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 32px Arial, sans-serif';
                ctx.textAlign = 'center';
                ctx.fillText(`幻灯片 ${slideNumber}`, 512, 60);
                
                // 绘制文本内容
                ctx.font = '24px Arial, sans-serif';
                ctx.textAlign = 'left';
                ctx.fillStyle = '#34495e';
                
                for (let i = 0; i < Math.min(textElements.length, 10); i++) {
                    const textContent = textElements[i].textContent.trim();
                    if (textContent && textContent.length > 0) {
                        // 处理长文本，自动换行
                        const words = textContent.split(' ');
                        let line = '';
                        const maxWidth = 900;
                        
                        for (let j = 0; j < words.length; j++) {
                            const testLine = line + words[j] + ' ';
                            const metrics = ctx.measureText(testLine);
                            
                            if (metrics.width > maxWidth && line !== '') {
                                ctx.fillText(line, 60, yPosition);
                                line = words[j] + ' ';
                                yPosition += 35;
                            } else {
                                line = testLine;
                            }
                            
                            if (yPosition > 700) break; // 防止文本超出画布
                        }
                        
                        if (line.trim() !== '') {
                            ctx.fillText(line, 60, yPosition);
                            yPosition += 35;
                        }
                        
                        yPosition += 10; // 段落间距
                    }
                }
                
                // 如果没有找到文本内容，显示默认信息
                if (textElements.length === 0) {
                    ctx.fillStyle = '#7f8c8d';
                    ctx.font = '20px Arial, sans-serif';
                    ctx.textAlign = 'center';
                    ctx.fillText('此幻灯片包含图片、图表或其他媒体内容', 512, 400);
                    ctx.fillText('(当前版本暂不支持显示)', 512, 430);
                }
                
            } catch (error) {
                console.warn('解析幻灯片内容失败，使用默认显示:', error);
                
                // 如果解析失败，显示基本信息
                ctx.fillStyle = '#e74c3c';
                ctx.font = '20px Arial, sans-serif';
                ctx.textAlign = 'center';
                ctx.fillText('幻灯片内容解析中...', 512, 400);
            }
            
            // 添加文件信息
            ctx.fillStyle = '#95a5a6';
            ctx.font = '14px Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(`来源: ${fileName}`, 512, 740);
            ctx.fillText(`生成时间: ${new Date().toLocaleString()}`, 512, 760);
            
            return canvas.toDataURL('image/png');
        }
        
        // 显示幻灯片
        function displaySlides(images, fileName) {
            slidesContainer.innerHTML = '';
            
            images.forEach((imageUrl, index) => {
                const slideDiv = document.createElement('div');
                slideDiv.className = 'slide';
                
                slideDiv.innerHTML = `
                    <div class="slide-title">幻灯片 ${index + 1}</div>
                    <img src="${imageUrl}" alt="幻灯片 ${index + 1}">
                `;
                
                slidesContainer.appendChild(slideDiv);
            });
            
            showStatus(`成功解析 ${fileName}，共生成 ${images.length} 张幻灯片预览`, 'success');
            hideProgress();
        }
        
        // 处理文件选择
        fileInput.addEventListener('change', async (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            // 检查文件类型
            const isValidPPT = file.type === 'application/vnd.ms-powerpoint' ||
                              file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation' ||
                              file.name.toLowerCase().endsWith('.ppt') ||
                              file.name.toLowerCase().endsWith('.pptx');
            
            if (!isValidPPT) {
                showStatus('不支持的文件格式，请上传PPT或PPTX文件', 'error');
                return;
            }
            
            try {
                showStatus(`正在处理文件: ${file.name}`, 'info');
                updateProgress(10);
                
                if (file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation' ||
                    file.name.toLowerCase().endsWith('.pptx')) {
                    
                    const images = await parsePPTXFile(file);
                    displaySlides(images, file.name);
                    
                } else {
                    showStatus('老版本PPT文件暂不支持解析，请转换为PPTX格式', 'error');
                    hideProgress();
                }
                
            } catch (error) {
                console.error('处理文件失败:', error);
                showStatus(`处理文件失败: ${error.message}`, 'error');
                hideProgress();
            }
        });
        
        // 拖拽支持
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
            uploadArea.style.backgroundColor = '#f8f9fa';
        });
        
        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = 'transparent';
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = 'transparent';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                fileInput.dispatchEvent(new Event('change'));
            }
        });
    </script>
</body>
</html>
