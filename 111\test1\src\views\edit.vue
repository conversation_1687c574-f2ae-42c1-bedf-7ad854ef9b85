<template>
  <div class="edit-page">
    <div class="add-question">
      <h2>新增题目</h2>
      <form @submit.prevent="submitNewQuestion">
        <div>
          <label for="content">题目内容：</label>
          <input type="text" v-model="newQuestion.content" placeholder="请输入题目内容" required />
        </div>
        <div>
          <label>题目类型：</label>
          <input type="text" :value="type" disabled />
        </div>
        <div>
          <label for="answer">答案：</label>
          <input type="text" v-model="newQuestion.answer" placeholder="请输入答案" required />
        </div>
        <div>
          <label for="options">选项：</label>
          <div v-for="(option, index) in newQuestion.options" :key="index">
            <input v-model="newQuestion.options[index]" placeholder="请输入选项" />
          </div>
        </div>
        <button type="submit">提交新增题目</button>
      </form>
    </div>
    <h1>编辑题目 - {{ type }}</h1>

    <div v-if="loading">加载中...</div>
    <div v-else-if="questions.length === 0">未找到该类型的题目。</div>

    <ul v-else>
      <li v-for="question in questions" :key="question.id" class="question-item">
        <!-- 题目编辑 -->
        <div v-if="editingId === question.id">
          <input v-model="editedContent" />
          <button @click="updateQuestion(question.id)">保存</button>
          <button @click="cancelEdit">取消</button>
        </div>
        <div v-else>
          <strong>{{ question.content }}</strong>
          <button @click="startEdit(question)">编辑题目</button>
          <button @click="deleteQuestion(question.id)">删除题目</button>
        </div>

        <!-- 显示选项 -->
        <ul class="options-list">
          <li v-for="option in question.options" :key="option.id">
            <div v-if="editingOptionId === option.id">
              <input v-model="editedOptionContent" />
              <button @click="updateOption(option.id)">保存</button>
              <button @click="cancelOptionEdit">取消</button>
            </div>
            <div v-else>
              {{ option.content }}
              <button @click="startOptionEdit(option)">编辑</button>
              <button @click="deleteOption(option.id, question.id)">删除</button>
            </div>
          </li>
        </ul>

        <!-- 添加新选项 -->
        <div class="add-option">
          <input v-model="newOptionContent" placeholder="新选项内容" />
          <button @click="addOption(question.id)">添加选项</button>
        </div>

        <!-- 显示和编辑答案 -->
        <div class="answer-section">
          <strong>答案：</strong>
          <input v-model="question.answer" @change="updateAnswer(question.id, question.answer)" />
        </div>

        <hr />
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import axios from "axios";

export default defineComponent({
  name: "EditPage",
  data() {
    return {
      newQuestion: {
        content: "",
        type: "",
        answer: "",
        options: ["", "", "", ""],
      },
    };
  },
  methods: {
    // 添加选项
    addOption(questionId: number) {
      if (!this.newOptionContent.trim()) return;
      axios
        .post("http://localhost:3000/api/options", {
          questionId,
          content: this.newOptionContent,
        })
        .then((response) => {
          const newOption = { id: response.data.id, content: this.newOptionContent };
          const question = this.questions.find((q) => q.id === questionId);
          if (question) question.options.push(newOption);
          this.newOptionContent = "";
        })
        .catch((error) => {
          console.error("添加选项失败:", error);
        });
    },
    
    //新增题目
    async submitNewQuestion() {
      try {
        // 向后端提交题目，包括题目类型
        const { data } = await axios.post('http://localhost:3000/api/questions', {
          content: this.newQuestion.content,
          type: this.type,  // 确保传递了题目类型
        });

        const questionId = data.id;

        // 提交选项
        for (let option of this.newQuestion.options) {
          await axios.post('http://localhost:3000/api/options', {
            questionId,
            content: option,
          });
        }

        // 提交答案
        await axios.post('http://localhost:3000/api/answers', {
          questionId,
          answer: this.newQuestion.answer,
        });

        alert('题目添加成功');
        // 清空表单
        this.newQuestion.content = "";
        this.newQuestion.type = "";  // 清空题目类型
        this.newQuestion.answer = "";
        this.newQuestion.options = ["", "", "", ""];  // 清空选项

        // 重新加载题目
        this.fetchQuestions();  // 重新加载题目，确保类型是更新的
      } catch (error) {
        console.error("新增题目失败:", error);
        alert("新增题目失败");
      }
    }

  },
  setup() {
    const route = useRoute();
    const type = route.query.type as string;
    const questions = ref<
      { id: number; content: string; type: string; options: { id: number; content: string }[]; answer: string }[]
    >([]);
    const loading = ref(true);

    // 题目编辑状态
    const editingId = ref<number | null>(null);
    const editedContent = ref<string>("");

    // 选项编辑状态
    const editingOptionId = ref<number | null>(null);
    const editedOptionContent = ref<string>("");

    // 新选项内容
    const newOptionContent = ref<string>("");

    // 获取题目、选项和答案
    const fetchQuestions = async () => {
      try {
        const questionRes = await axios.get("http://localhost:3000/api/questions", { params: { type } });
        const questionsData = questionRes.data.data;

        const optionRes = await axios.get("http://localhost:3000/api/options");
        const optionsData = optionRes.data.data;

        const answerRes = await axios.get("http://localhost:3000/api/answer");
        const answersData = answerRes.data.data;

        questions.value = questionsData.map((q: any) => ({
          ...q,
          options: optionsData.filter((opt: any) => opt.question_id === q.id),
          answer: answersData.find((ans: any) => ans.id === q.id)?.answer || "",
        }));
      } catch (error) {
        console.error("获取数据失败:", error);
      } finally {
        loading.value = false;
      }
    };

    const startEdit = (question: { id: number; content: string }) => {
      editingId.value = question.id;
      editedContent.value = question.content;
    };

    const cancelEdit = () => {
      editingId.value = null;
      editedContent.value = "";
    };

    const updateQuestion = async (id: number) => {
      try {
        await axios.put(`http://localhost:3000/api/questions/${id}`, { content: editedContent.value });
        const question = questions.value.find((q) => q.id === id);
        if (question) question.content = editedContent.value;
        cancelEdit();
        alert('题目更新成功');
      } catch (error) {
        console.error('更新题目失败:', error);
        alert('更新题目失败');
      }
    };

    const deleteQuestion = async (id: number) => {
      if (!confirm("确定删除该题目吗？")) return;
      try {
        await axios.delete(`http://localhost:3000/api/questions/${id}`);
        questions.value = questions.value.filter((q) => q.id !== id);
      } catch (error) {
        console.error("删除题目失败:", error);
      }
    };

    const startOptionEdit = (option: { id: number; content: string }) => {
      editingOptionId.value = option.id;
      editedOptionContent.value = option.content;
    };

    const cancelOptionEdit = () => {
      editingOptionId.value = null;
      editedOptionContent.value = "";
    };

    const updateOption = async (id: number) => {
      try {
        await axios.put(`http://localhost:3000/api/options/${id}`, { content: editedOptionContent.value });
        for (const q of questions.value) {
          const option = q.options.find((opt) => opt.id === id);
          if (option) option.content = editedOptionContent.value;
        }
        cancelOptionEdit();
        alert('选项更新成功');
      } catch (error) {
        console.error('更新选项失败:', error);
        alert('更新选项失败');
      }
    };

    const deleteOption = async (optionId: number, questionId: number) => {
      try {
        await axios.delete(`http://localhost:3000/api/options/${optionId}`);
        const question = questions.value.find((q) => q.id === questionId);
        if (question) question.options = question.options.filter((opt) => opt.id !== optionId);
      } catch (error) {
        console.error("删除选项失败:", error);
      }
    };

    const updateAnswer = async (questionId: number, newAnswer: string) => {
      try {
        await axios.put(`http://localhost:3000/api/answers/${questionId}`, { answer: newAnswer });
        alert('答案更新成功');
      } catch (error) {
        console.error('更新答案失败:', error);
        alert('更新答案失败');
      }
    };

    onMounted(() => fetchQuestions());

    return {
      type,
      questions,
      loading,
      editingId,
      editedContent,
      editingOptionId,
      editedOptionContent,
      newOptionContent,
      startEdit,
      cancelEdit,
      updateQuestion,
      deleteQuestion,
      startOptionEdit,
      cancelOptionEdit,
      updateOption,
      deleteOption,
      updateAnswer,
      fetchQuestions,
    };
  },
});
</script>

<style scoped>
.edit-page {
  padding: 20px;
}

.question-item {
  margin-bottom: 20px;
  border: 1px solid #ddd;
  padding: 15px;
  border-radius: 5px;
}

.options-list {
  list-style: none;
  padding: 0;
}

.options-list li {
  margin-bottom: 5px;
}

.add-option {
  margin-top: 10px;
}

.answer-section {
  margin-top: 10px;
  font-weight: bold;
}
</style>
