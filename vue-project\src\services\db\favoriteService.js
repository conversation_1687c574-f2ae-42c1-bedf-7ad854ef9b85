import { getDB, STORES } from './index'

/**
 * 添加收藏
 * @param {number} resourceId - 资源ID
 * @param {string} userId - 用户ID，默认为'current_user'
 * @returns {Promise<Object>} 收藏记录
 */
export async function addFavorite(resourceId, userId = 'current_user') {
  const db = await getDB()
  
  try {
    // 检查是否已经收藏
    const existing = await getFavorite(resourceId, userId)
    if (existing) {
      throw new Error('已经收藏过此资源')
    }
    
    const favorite = {
      resourceId: Number(resourceId),
      userId: userId,
      createdAt: new Date().toISOString()
    }
    
    const result = await db.add(STORES.FAVORITES, favorite)
    return { ...favorite, id: result }
  } catch (error) {
    console.error('添加收藏失败:', error)
    throw error
  }
}

/**
 * 取消收藏
 * @param {number} resourceId - 资源ID
 * @param {string} userId - 用户ID，默认为'current_user'
 * @returns {Promise<boolean>} 是否成功取消收藏
 */
export async function removeFavorite(resourceId, userId = 'current_user') {
  const db = await getDB()
  
  try {
    const favorite = await getFavorite(resourceId, userId)
    if (!favorite) {
      return false
    }
    
    await db.delete(STORES.FAVORITES, favorite.id)
    return true
  } catch (error) {
    console.error('取消收藏失败:', error)
    throw error
  }
}

/**
 * 检查是否已收藏
 * @param {number} resourceId - 资源ID
 * @param {string} userId - 用户ID，默认为'current_user'
 * @returns {Promise<Object|null>} 收藏记录或null
 */
export async function getFavorite(resourceId, userId = 'current_user') {
  const db = await getDB()
  
  try {
    const tx = db.transaction(STORES.FAVORITES, 'readonly')
    const store = tx.objectStore(STORES.FAVORITES)
    const index = store.index('userResource')
    
    const favorite = await index.get([userId, Number(resourceId)])
    return favorite || null
  } catch (error) {
    console.error('检查收藏状态失败:', error)
    return null
  }
}

/**
 * 获取用户的所有收藏
 * @param {string} userId - 用户ID，默认为'current_user'
 * @returns {Promise<Array>} 收藏列表
 */
export async function getUserFavorites(userId = 'current_user') {
  const db = await getDB()
  
  try {
    const tx = db.transaction(STORES.FAVORITES, 'readonly')
    const store = tx.objectStore(STORES.FAVORITES)
    const index = store.index('userId')
    
    const favorites = await index.getAll(userId)
    return favorites.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  } catch (error) {
    console.error('获取用户收藏失败:', error)
    return []
  }
}

/**
 * 获取资源的收藏数量
 * @param {number} resourceId - 资源ID
 * @returns {Promise<number>} 收藏数量
 */
export async function getFavoriteCount(resourceId) {
  const db = await getDB()
  
  try {
    const tx = db.transaction(STORES.FAVORITES, 'readonly')
    const store = tx.objectStore(STORES.FAVORITES)
    const index = store.index('resourceId')
    
    const favorites = await index.getAll(Number(resourceId))
    return favorites.length
  } catch (error) {
    console.error('获取收藏数量失败:', error)
    return 0
  }
}

/**
 * 切换收藏状态
 * @param {number} resourceId - 资源ID
 * @param {string} userId - 用户ID，默认为'current_user'
 * @returns {Promise<Object>} 操作结果 { isFavorited: boolean, message: string }
 */
export async function toggleFavorite(resourceId, userId = 'current_user') {
  try {
    const existing = await getFavorite(resourceId, userId)
    
    if (existing) {
      await removeFavorite(resourceId, userId)
      return { isFavorited: false, message: '已取消收藏' }
    } else {
      await addFavorite(resourceId, userId)
      return { isFavorited: true, message: '收藏成功' }
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error)
    throw error
  }
}

/**
 * 批量检查收藏状态
 * @param {Array<number>} resourceIds - 资源ID数组
 * @param {string} userId - 用户ID，默认为'current_user'
 * @returns {Promise<Object>} 收藏状态映射 { resourceId: boolean }
 */
export async function checkFavoriteStatus(resourceIds, userId = 'current_user') {
  const db = await getDB()
  const statusMap = {}
  
  try {
    const tx = db.transaction(STORES.FAVORITES, 'readonly')
    const store = tx.objectStore(STORES.FAVORITES)
    const index = store.index('userResource')
    
    for (const resourceId of resourceIds) {
      const favorite = await index.get([userId, Number(resourceId)])
      statusMap[resourceId] = !!favorite
    }
    
    return statusMap
  } catch (error) {
    console.error('批量检查收藏状态失败:', error)
    // 返回默认状态（全部未收藏）
    resourceIds.forEach(id => {
      statusMap[id] = false
    })
    return statusMap
  }
}
