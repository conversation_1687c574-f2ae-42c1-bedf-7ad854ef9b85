{"name": "test1", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "test": "vite build --mode test", "eval": "vite build --mode eval", "preview": "vite preview", "lint": "eslint . --ext .ts,.vue && prettier --check /**/*.{vue,ts}", "lint:fix": "eslint . --ext .ts,.vue --fix && prettier --write /**/*.{vue,ts}"}, "dependencies": {"axios": "^1.7.9", "body-parser": "^1.20.3", "cors": "^2.8.5", "express": "^4.21.2", "mysql2": "^3.12.0", "path": "^0.12.7", "view-ui-plus": "^1.3.19", "vue": "^3.5.13", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/express": "^5.0.0", "@types/node": "^22.10.2", "@vitejs/plugin-vue": "^5.2.1", "@vue/cli-service": "^5.0.8", "@vue/tsconfig": "^0.7.0", "concurrently": "^9.1.2", "npm-run-all2": "^7.0.2", "ts-node": "^10.9.2", "typescript": "~5.6.3", "vite": "^6.0.5", "vite-plugin-vue-devtools": "^7.6.8", "vue-tsc": "^2.1.10"}}