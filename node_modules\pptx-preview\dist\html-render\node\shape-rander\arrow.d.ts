import ShapeNode from "../../../reader/node/ShapeNode";
import { TextArea } from "./shape-common";
export declare function createRightArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixRightArrowText(shapeNode: ShapeNode): TextArea;
export declare function createLeftArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixLeftArrowText(shapeNode: ShapeNode): TextArea;
export declare function createUpArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixUpArrowText(shapeNode: ShapeNode): TextArea;
export declare function createDownArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixDownArrowText(shapeNode: ShapeNode): TextArea;
export declare function createLeftRightArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixLeftRightArrowText(shapeNode: ShapeNode): TextArea;
export declare function createUpDownArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixUpDownArrowText(shapeNode: ShapeNode): TextArea;
export declare function createQuadArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixQuadArrowText(shapeNode: ShapeNode): TextArea;
export declare function createLeftRightUpArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixLeftRightUpArrowText(shapeNode: ShapeNode): TextArea;
export declare function createBentArrow(shapeNode: ShapeNode): SVGElement;
export declare function createUturnArrow(shapeNode: ShapeNode): SVGElement;
export declare function createLeftUpArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixLeftUpArrowText(shapeNode: ShapeNode): TextArea;
export declare function createBentUpArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixBentUpArrowText(shapeNode: ShapeNode): TextArea;
export declare function createCurvedRightArrow(shapeNode: ShapeNode): SVGElement;
export declare function createCurvedLeftArrow(shapeNode: ShapeNode): SVGElement;
export declare function createCurvedUpArrow(shapeNode: ShapeNode): SVGElement;
export declare function createCurvedDownArrow(shapeNode: ShapeNode): SVGElement;
export declare function createStripedRightArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixStripedRightArrowText(shapeNode: ShapeNode): TextArea;
export declare function createNotchedRightArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixNotchedRightArrowText(shapeNode: ShapeNode): TextArea;
export declare function createHomePlateArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixHomePlateArrowText(shapeNode: ShapeNode): TextArea;
export declare function createChevronArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixHomeChevronArrowText(shapeNode: ShapeNode): TextArea;
export declare function createRightArrowCalloutArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixRightArrowCalloutArrowText(shapeNode: ShapeNode): TextArea;
export declare function createLeftArrowCalloutArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixLeftArrowCalloutArrowText(shapeNode: ShapeNode): TextArea;
export declare function createUpArrowCalloutArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixUpArrowCalloutArrowText(shapeNode: ShapeNode): TextArea;
export declare function createDownArrowCalloutArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixDownArrowCalloutArrowText(shapeNode: ShapeNode): TextArea;
export declare function createLeftRightArrowCalloutArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixLeftRightArrowCalloutArrowText(shapeNode: ShapeNode): TextArea;
export declare function createQuadArrowCalloutArrow(shapeNode: ShapeNode): SVGElement;
export declare function fixQuadArrowCalloutArrowText(shapeNode: ShapeNode): TextArea;
