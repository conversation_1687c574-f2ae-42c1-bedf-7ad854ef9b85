<template>
  <div class="login-container">
    <h1>数学文化竞赛考试</h1>
    <form @submit.prevent="handleLogin">
      <div class="form-group">
        <label for="username">工号</label>
        <input
          id="username"
          v-model="username"
          type="text"
          placeholder="请输入工号"
          required
        />
      </div>
      <div class="form-group">
        <label for="password">密码</label>
        <input
          id="password"
          v-model="password"
          type="password"
          placeholder="请输入密码"
          required
        />
      </div>
      <div class="form-group agreement">
        <input
          id="agreement"
          type="checkbox"
          v-model="isAgreed"
        />
        <label for="agreement">
          已阅读并同意
          <button type="button" class="policy-button" @click="showPolicy">
            《“云请”用户服务及隐私政策》
          </button>
        </label>
      </div>
      <div class="button_login">
        <button type="submit" :disabled="!isAgreed || !username || !password">登录</button>
      </div>
      <p v-if="errorMessage" class="error-message">{{ errorMessage }}</p>
    </form>

    <!-- 隐私政策模态框 -->
    <div v-if="isPolicyVisible" class="modal-overlay" @click="hidePolicy">
      <div class="modal-content" @click.stop>
        <h2>“云请”用户服务及隐私政策</h2>
        <p>
          这里是隐私政策。
        </p>
        <button @click="hidePolicy">关闭</button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from "vue";
import { useRouter } from "vue-router";
import axios from "axios";

export default defineComponent({
  name: "Login",
  setup() {
    const username = ref("");
    const password = ref("");
    const isAgreed = ref(false);
    const errorMessage = ref("");
    const isPolicyVisible = ref(false);
    const router = useRouter();
    
    // 登录接口调用
    const handleLogin = async () => {
      try {
        // 向后端发送请求验证工号和密码
        const response = await axios.post("http://localhost:3000/api/people", {
          username: username.value,
          password: password.value,
        });

        if (response.data.success) {
          const { redirectTo } = response.data;
          if (isAgreed.value) {
            // 跳转到 quizPage 页面
            router.push(redirectTo);
          } else {
            errorMessage.value = "请阅读并同意《“云请”用户服务及隐私政策》";
          }
        } else {
          errorMessage.value = response.data.message || "用户名或密码错误！";
        }
      } catch (error) {
        console.error("登录请求错误:", error);
        errorMessage.value = "登录时发生错误，请稍后再试！";
      }
    };

    const showPolicy = () => {
      isPolicyVisible.value = true;
    };

    const hidePolicy = () => {
      isPolicyVisible.value = false;
    };

    return {
      username,
      password,
      isAgreed,
      errorMessage,
      isPolicyVisible,
      handleLogin,
      showPolicy,
      hidePolicy,
    };
  },
});
</script>


<style scoped>
.login-container {
  max-width: 400px;
  margin: 100px auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.login-container title{
  margin-left: 0;
}

.login-container input{
  color: rgb(46, 44, 44);
}

input::placeholder{
  color: rgb(254, 28, 28);
}

h1 {
  margin-left: 0;
}

.form-group {
  margin-bottom: 15px;
}

label {
  display: block;
  margin-bottom: 5px;
}

input {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
  border: 1px solid transparent; 
  background-color: rgb(247, 247, 247);
  outline: none; 
  transition: box-shadow 0.3s ease, border-color 0.3s ease; 
  border-radius: 5px;
}

input:focus {
  border-color: #ff6b6b; 
  box-shadow: 0 0 5px 2px rgba(255, 107, 107, 0.5),
              0 0 15px 5px rgba(255, 107, 107, 0.3),
              0 0 25px 10px rgba(255, 107, 107, 0.1); 
}

button {
  width: 100%;
  padding: 10px;
  background-color: rgb(254, 28, 28);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

button:hover {
  background-color: rgb(204, 17, 17);
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.policy-button {
  all: unset; 
  color: rgb(92, 134, 226); 
  cursor: default;  
  text-decoration: none; 
}

.policy-button:visited {
  color: rgb(254, 28, 28); 
}

.policy-button:hover,
.policy-button:focus,
.policy-button:active {
  color: rgb(254, 28, 28); 
  background: none; 
  text-decoration: none; 
}


.error-message {
  color: red;
  margin-top: 10px;
  text-align: center;
}

.agreement {
  position: relative;
  display: flex;
  align-items: center;
  height: 15px;
}

.agreement input[type="checkbox"] {
  accent-color: rgb(254, 28, 28); 
}

.agreement input {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 15px;
  height: 15px; 
}

.agreement label {
  position: absolute;
  margin: 0;
  top: 0;
  height: 15px;
  left: 25px;
  display: inline-block;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 10px;
  max-width: 500px;
  width: 80%;
  text-align: center;
}

.modal-content h2 {
  margin-top: 0;
}

.button_login{
  margin-top: 30px;
}

</style>
