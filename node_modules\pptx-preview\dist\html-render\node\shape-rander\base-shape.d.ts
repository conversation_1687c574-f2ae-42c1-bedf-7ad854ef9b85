import { TextArea } from "./shape-common";
import ShapeNode from "../../../reader/node/ShapeNode";
export declare function createParallelogram(shapeNode: any): SVGElement;
export declare function createTrapezoid(shapeNode: ShapeNode): SVGElement;
export declare function createTriangle(shapeNode: any): SVGElement;
export declare function createRightTriangle(shapeNode: any): SVGElement;
export declare function createEllipse(shapeNode: any): SVGElement;
export declare function fixParallelogramText(shapeNode: ShapeNode): TextArea;
export declare function fixTrapezoidText(shapeNode: ShapeNode): TextArea;
export declare function createDiamond(shapeNode: ShapeNode): SVGElement;
export declare function fixDiamondText(shapeNode: ShapeNode): TextArea;
export declare function createPentagon(shapeNode: ShapeNode): SVGElement;
export declare function fixPentagonText(shapeNode: ShapeNode): {
    top: number;
    bottom: number;
    left: number;
    right: number;
    w: number;
    h: number;
};
export declare function createCustomGeom(shapeNode: ShapeNode): SVGElement;
export declare function createHexagon(shapeNode: ShapeNode): SVGElement;
export declare function fixHexagonText(shapeNode: ShapeNode): TextArea;
export declare function createHeptagon(shapeNode: ShapeNode): SVGElement;
export declare function fixHeptagonText(shapeNode: ShapeNode): TextArea;
export declare function createOctagon(shapeNode: ShapeNode): SVGElement;
export declare function fixOctagonText(shapeNode: ShapeNode): TextArea;
export declare function createDecagon(shapeNode: ShapeNode): SVGElement;
export declare function fixDecagonText(shapeNode: ShapeNode): TextArea;
export declare function createDodecagon(shapeNode: ShapeNode): SVGElement;
export declare function fixDodecagonText(shapeNode: ShapeNode): TextArea;
export declare function calcEllipseCoordinate(angle: any, cx: any, cy: any, rx: any, ry: any): any[];
export declare function calcEllipseLargeArcFlag(startAngle: any, endAngle: any): number;
export declare function createPie(shapeNode: ShapeNode): SVGElement;
export declare function createChord(shapeNode: ShapeNode): SVGElement;
export declare function fixPieText(shapeNode: ShapeNode): TextArea;
export declare function createTeardrop(shapeNode: ShapeNode): SVGElement;
export declare function createFrame(shapeNode: ShapeNode): SVGElement;
export declare function fixFrameText(shapeNode: ShapeNode): TextArea;
export declare function createHalfFrame(shapeNode: ShapeNode): SVGElement;
export declare function createCorner(shapeNode: ShapeNode): SVGElement;
export declare function fixCorner(shapeNode: ShapeNode): TextArea;
export declare function createDiagStripe(shapeNode: ShapeNode): SVGElement;
export declare function fixDiagStripeText(shapeNode: ShapeNode): TextArea;
export declare function createPlus(shapeNode: ShapeNode): SVGElement;
export declare function fixPlusText(shapeNode: ShapeNode): TextArea;
export declare function createPlaque(shapeNode: ShapeNode): SVGElement;
export declare function fixPlaqueText(shapeNode: ShapeNode): TextArea;
export declare function createCan(shapeNode: ShapeNode): SVGElement;
export declare function fixCanText(shapeNode: ShapeNode): TextArea;
export declare function createCube(shapeNode: ShapeNode): SVGElement;
export declare function fixCubeText(shapeNode: ShapeNode): TextArea;
export declare function createBevel(shapeNode: ShapeNode): SVGElement;
export declare function fixBevelText(shapeNode: ShapeNode): TextArea;
export declare function createDonut(shapeNode: ShapeNode): SVGElement;
export declare function createNoSmoking(shapeNode: ShapeNode): SVGElement;
export declare function createArc(shapeNode: ShapeNode): SVGElement;
export declare function createBracketPair(shapeNode: ShapeNode): SVGElement;
export declare function fixBracketPairTextarea(shapeNode: ShapeNode): {
    top: number;
    bottom: number;
    left: number;
    right: number;
    w: number;
    h: number;
};
export declare function createBracePair(shapeNode: ShapeNode): SVGElement;
export declare function fixBracePairTextarea(shapeNode: ShapeNode): {
    top: number;
    bottom: number;
    left: number;
    right: number;
    w: number;
    h: number;
};
export declare function createBlockArc(shapeNode: ShapeNode): SVGElement;
export declare function fixBlockArcTextarea(shapeNode: ShapeNode): {
    top: number;
    bottom: number;
    left: number;
    right: number;
    w: number;
    h: number;
};
export declare function createFoldedCorner(shapeNode: ShapeNode): SVGElement;
export declare function fixFoldedCornerTextarea(shapeNode: ShapeNode): {
    top: number;
    bottom: number;
    left: number;
    right: number;
    w: number;
    h: number;
};
