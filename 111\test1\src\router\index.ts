import { createRouter, createWebHistory } from "vue-router";
import Login from "../views/Login.vue";
import QuizPage from "../views/QuizPage.vue";
import adtor_QuizPage from "../views/adtor_QuizPage.vue";
import edit from "../views/edit.vue";

const routes = [
  { path: "/", component: Login },
  { path: "/quizPage", component: QuizPage },
  { path: "/adtor_QuizPage", component: adtor_QuizPage },
  { path: "/edit",name: "edit", component: edit },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;


