<template>
  <div class="quiz-page">
    <h1>管理中心</h1>
    <div class="pic"></div>
    <div class="search">
      <input id="search"
        type="text"
        placeholder="请输入以搜索"
        required
      />
    </div>
    <div class="edit">
      <div class="labels">
        <img class="labels_left" src="../images/1.png"  />
        <button class="labels_right" @click="goToEdit('金融规范知识')">
          金融规范知识
        </button>
      </div>
      <div class="lines"></div>
      <div class="labels">
        <img class="labels_left" src="../images/1.png"  />
        <button class="labels_right" @click="goToEdit('知识产权导论')">
          知识产权导论
        </button>
      </div>
      <div class="lines"></div>
      <div class="labels">
        <img class="labels_left" src="../images/1.png"  />
        <button class="labels_right" @click="goToEdit('高等数学')">
          高等数学
        </button>
      </div>
      <div class="lines"></div>
      <div class="labels">
        <img class="labels_left" src="../images/1.png"  />
        <button class="labels_right" @click="goToEdit('线性代数')">
          线性代数
        </button>
      </div>
      <div class="lines"></div>
      <div class="labels">
        <img class="labels_left" src="../images/1.png"  />
        <button class="labels_right" @click="goToEdit('概率论与数理统计')">
          概率论与数理统计
        </button>
      </div>
    </div>    
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import { useRouter } from "vue-router";

export default defineComponent({
  name: "QuizPage",
  setup() {
    const router = useRouter();

    // 跳转到 edit 页面并传递 type 参数
    const goToEdit = (type: string) => {
      router.push({ name: "edit", query: { type } });
    };

    return {
      goToEdit,
    };
  },
});
</script>

<style scoped>
.quiz-page {
  position: relative;
  align-items: center;
  text-align: center;
  max-width: 400px;
  height: 650px;
  margin: 100px auto;
  /* padding: 20px; */
  background-color: rgb(182, 181, 181);
}

.pic {
  width: 400px;
  height: 200px;
  background-color: antiquewhite;
  z-index: 999;
}

/* .quiz_contanier{
  position: absolute;
  padding: 20px;
  left: 20px;
  top: 150px;
  width: 400px;
  height: auto;
  flex-direction: column;
  justify-content: center;
} */

.search{
  position: absolute;
  border-radius: 20px;
  width: 360px;
  height: 45px;
  left: 20px;
  top: 110px;
  background-color: white;
  display: flex;
  justify-content: center; 
  align-items: center; 
}

.search input{

  width: 80%;
  padding: 8px;
  box-sizing: border-box;
  border: 1px solid transparent; 
  outline: none; 
  transition: box-shadow 0.3s ease, border-color 0.3s ease; 
}

.edit{
  position: absolute;
  padding-bottom: 15px;
  left: 20px;
  top: 210px;
  width: 360px;
  height: auto;
  flex-direction: column; 
  justify-content: center;
  align-items: center;
  background-color: white;
  border-radius: 15px;
}

.labels{
  display: flex;              
  align-items: center;        
  gap: 10px;                 
  margin-top: 15px;
  width: 340px;
  height: 40px;
  
}

.labels_left{  
  margin-top: 0;
  margin-left: 10px;
  width: 40px;
  height: 40px;
}

.labels_right{
  margin-top: 0;
  width: 290px;
  height: 40px;
  border-radius: 10px;
  text-align: left;
}

.lines{
  margin-top: 10px;
  margin-left: 15px;
  background-color: gray;
  width: 300px;
  height: 0.5px;
}

</style>

