<template>
  <div class="quiz-page">
    <h1>答题页面</h1>

    <div v-if="loading">加载中...</div>
    <div v-else-if="questions.length === 0">未找到该类型的题目。</div>

    <div v-else>
      <div v-if="currentQuestionIndex !== null" class="question">
        <!-- 题目居中显示 -->
        <div class="question-content">{{ currentQuestionIndex + 1 }}. {{ questions[currentQuestionIndex].content }}

          <!-- 单选按钮用于选择答案 -->
          <div v-for="(option, idx) in questions[currentQuestionIndex].options" :key="option.id" class="option">
            <input type="radio" :name="'question-' + currentQuestionIndex" :id="'option-' + idx"
              v-model="userQuestionAnswers[currentQuestionIndex]" :value="option.content" />
            <label :for="'option-' + idx">{{ option.content }}</label>
          </div>
        </div>
        <div class="navigation">
          <button @click="prevQuestion" :disabled="currentQuestionIndex === 0">上一题</button>
          <button @click="nextQuestion" :disabled="currentQuestionIndex === questions.length - 1">下一题</button>
        </div>
      </div>

      <!-- 答题卡 -->
      <button @click="toggleAnswerCard">答题卡</button>
      <div v-if="showAnswerCard" class="answer-card">
        <ul>
          <li v-for="(question, index) in questions" :key="question.id">
            <button @click="goToQuestion(index)">
              {{ index + 1 }}. {{ question.content }} - {{ userQuestionAnswers[index] ? '已答' : '未答' }}
            </button>
          </li>
        </ul>
      </div>

      <!-- 提交按钮 -->
      <button @click="submitQuiz" class="submit-btn">交卷</button>

      <!-- 提交后的答题结果 -->
      <div v-if="submitted" class="results">
        <h2>交卷结果</h2>
        <p>总题数：{{ questions.length }}</p>
        <p>答对题数：{{ correctAnswersCount }}</p>
        <h2>错误题目</h2>
        <div v-for="(question, index) in incorrectQuestions" :key="question.id">
          <p>{{ question.content }}</p>
          <div v-for="(option, idx) in question.options" :key="option.id">
            <p>{{ option.content }}</p>
          </div>
          <p>你的答案：{{ userQuestionAnswers[index] }}</p>
          <p>正确答案：{{ question.answer }}</p>
        </div>
      </div>
      <div v-show="analysis">
        <h2>DeepSeek 智能分析</h2>
        <p>{{ analysis }}</p>
      </div>

    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import axios from 'axios';
import { useRoute } from 'vue-router';

export default defineComponent({
  name: 'QuizPage',
  setup() {
    const route = useRoute();
    const type = route.query.type as string;

    const analysis = ref<string>(""); // 存储 DeepSeek 返回的分析结果
    const API_KEY = "***********************************"; // 你的 API Key
    const BASE_URL = "https://api.deepseek.com/v1"; // DeepSeek API 地址

    //调用deepseek
    const analyzeWithDeepSeek = async (questions: any[], userAnswers: string[]) => {
      try {
        // 结构化题目、用户答案、正确答案
        const questionData = questions.map((q, index) => ({
          题干: q.content,
          选项: q.options.map((opt: any) => opt.content),
          用户答案: userAnswers[index],
          正确答案: q.answer,
        }));

        console.log("📌 提交给 DeepSeek 的数据:", JSON.stringify(questionData, null, 2));

        const response = await axios.post(
          `${BASE_URL}/chat/completions`,
          {
            model: "deepseek-reasoner", // 指定 DeepSeek-R1 模型
            messages: [
              {
                role: "system",
                content: "你是一个智能教育助手，帮助用户分析答题情况并提供学习建议。",
              },
              {
                role: "user",
                content: `请分析以下用户的答题情况，并提供错误分析、薄弱知识点、学习建议：
            
            ${JSON.stringify(questionData, null, 2)}`,
              },
            ],
          },
          {
            headers: {
              "Authorization": `Bearer ${API_KEY}`,
              "Content-Type": "application/json",
            },
          }
        );

        console.log("📌 DeepSeek API 响应:", response.data);
        return response.data.choices[0].message.content;
      } catch (error) {
        console.error("DeepSeek API 调用失败:", error.response?.data || error);
        return "分析失败，请稍后重试。";
      }
    };


    // 题目、选项和答案数据
    const questions = ref<any[]>([]);
    const userQuestionAnswers = ref<string[]>([]); // 每个问题的答案
    const loading = ref(true);
    const showAnswerCard = ref(false);
    const submitted = ref(false);
    const incorrectQuestions = ref<any[]>([]); // 错误题目
    const correctAnswersCount = ref(0); // 答对题数

    // 控制当前显示的题目
    const currentQuestionIndex = ref<number | null>(0);

    // 获取题目数据
    const fetchQuestions = async () => {
      try {
        const questionRes = await axios.get('http://localhost:3000/api/questions', {
          params: { type },
        });
        const questionsData = questionRes.data.data;

        const optionRes = await axios.get('http://localhost:3000/api/options');
        const optionsData = optionRes.data.data;

        const answerRes = await axios.get('http://localhost:3000/api/answer');
        const answersData = answerRes.data.data;

        // 将题目、选项和答案绑定在一起
        questions.value = questionsData.map((q: any) => ({
          ...q,
          options: optionsData.filter((opt: any) => opt.question_id === q.id),
          answer: answersData.find((ans: any) => ans.id === q.id)?.answer || '',
        }));

        // 初始化用户答案数组
        userQuestionAnswers.value = new Array(questions.value.length).fill('');
      } catch (error) {
        console.error('加载题目失败:', error);
      } finally {
        loading.value = false;
      }
    };

    // 切换到上一题
    const prevQuestion = () => {
      if (currentQuestionIndex.value > 0) {
        currentQuestionIndex.value -= 1;
      }
    };

    // 切换到下一题
    const nextQuestion = () => {
      if (currentQuestionIndex.value < questions.value.length - 1) {
        currentQuestionIndex.value += 1;
      }
    };

    // 跳转到特定题目
    const goToQuestion = (index: number) => {
      currentQuestionIndex.value = index;
    };

    // 显示答题卡
    const toggleAnswerCard = () => {
      showAnswerCard.value = !showAnswerCard.value;
    };

    // 提交试卷
    const submitQuiz = async () => {
      console.log("📌 submitQuiz 被调用了");

      submitted.value = true;
      evaluateAnswers();

      const userAnswers = userQuestionAnswers.value;

      // ✅ 传入完整的 questions 数据
      const deepSeekResult = await analyzeWithDeepSeek(questions.value, userAnswers);
      analysis.value = deepSeekResult;
    };

    // 评估用户答案
    const evaluateAnswers = () => {
      correctAnswersCount.value = 0;
      incorrectQuestions.value = [];

      questions.value.forEach((question, index) => {
        if (userQuestionAnswers.value[index].charAt(0) === question.answer) {
          correctAnswersCount.value += 1;
        } else {
          incorrectQuestions.value.push(question);
        }
      });
    };

    // 页面加载后请求题目数据
    onMounted(() => {
      fetchQuestions();
    });

    return {
      questions,
      userQuestionAnswers,
      loading,
      currentQuestionIndex,
      showAnswerCard,
      submitted,
      incorrectQuestions,
      correctAnswersCount,
      prevQuestion,
      nextQuestion,
      goToQuestion,
      toggleAnswerCard,
      submitQuiz,
      analysis,
    };
  },
});
</script>

<style scoped>
.quiz-page {
  padding: 20px;
  text-align: center;
}

.question {
  margin-bottom: 20px;
  text-align: center;
}

.question-content {
  display: inline-block;
  text-align: center;
}

.option {
  text-align: left;
}

button {
  padding: 10px;
  margin: 5px;
  cursor: pointer;
}

button:disabled {
  cursor: not-allowed;
  background-color: #ccc;
}

.answer-card {
  margin-top: 20px;
  background-color: #f9f9f9;
  padding: 10px;
  border: 1px solid #ddd;
}

.submit-btn {
  margin-top: 20px;
  background-color: #4caf50;
  color: white;
}

.results {
  margin-top: 20px;
  background-color: #f9f9f9;
  padding: 10px;
  border: 1px solid #ddd;
}

.results p {
  margin: 5px 0;
}
</style>
