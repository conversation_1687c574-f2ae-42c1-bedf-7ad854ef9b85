import { openDB } from 'idb';
import { initDefaultUsers } from './userService';

// 数据库名称和版本
const DB_NAME = 'teaching_platform_db';
const DB_VERSION = 4; // 增加版本号以触发数据库升级 // 更新版本号以触发数据库升级

// 存储对象名称
const STORES = {
  RESOURCES: 'resources',
  RESOURCE_COMMENTS: 'resource_comments',
  FAVORITES: 'favorites',               // 收藏功能
  VOTES: 'votes',
  VOTE_RESULTS: 'vote_results',
  NOTES: 'notes',
  QUESTIONS: 'questions',
  EXERCISES: 'exercises',
  EXERCISE_RESULTS: 'exercise_results',
  USER_PROFILES: 'user_profiles',
  SETTINGS: 'settings',
  // 新增答题系统相关存储
  QUIZ_QUESTIONS: 'quiz_questions',     // 题目存储
  QUIZ_ANSWERS: 'quiz_answers',         // 学生答案存储
  QUIZ_STATS: 'quiz_stats',             // 答题统计数据
  EXAMS: 'exams'                        // 考试数据存储
};

// 数据库实例
let dbInstance = null;

/**
 * 初始化数据库
 * @returns {Promise<IDBDatabase>}
 */
export async function initDatabase() {
  if (dbInstance) return dbInstance;

  try {
    // 增加错误处理，包装openDB调用
    try {
      dbInstance = await openDB(DB_NAME, DB_VERSION, {
        upgrade(db, oldVersion, newVersion, transaction) {
          console.log('创建/升级数据库...', oldVersion, newVersion);

          // 资源库
          if (!db.objectStoreNames.contains(STORES.RESOURCES)) {
            const resourceStore = db.createObjectStore(STORES.RESOURCES, { keyPath: 'id', autoIncrement: true });
            resourceStore.createIndex('type', 'type', { unique: false });
            resourceStore.createIndex('subject', 'subject', { unique: false });
            resourceStore.createIndex('uploadTime', 'uploadTime', { unique: false });
          }

          // 资源评论
          if (!db.objectStoreNames.contains(STORES.RESOURCE_COMMENTS)) {
            const commentStore = db.createObjectStore(STORES.RESOURCE_COMMENTS, { keyPath: 'id', autoIncrement: true });
            commentStore.createIndex('resourceId', 'resourceId', { unique: false });
            commentStore.createIndex('userId', 'userId', { unique: false });
            commentStore.createIndex('time', 'time', { unique: false });
          }

          // 收藏功能
          if (!db.objectStoreNames.contains(STORES.FAVORITES)) {
            const favoriteStore = db.createObjectStore(STORES.FAVORITES, { keyPath: 'id', autoIncrement: true });
            favoriteStore.createIndex('resourceId', 'resourceId', { unique: false });
            favoriteStore.createIndex('userId', 'userId', { unique: false });
            favoriteStore.createIndex('createdAt', 'createdAt', { unique: false });
            favoriteStore.createIndex('userResource', ['userId', 'resourceId'], { unique: true }); // 复合索引，确保用户不能重复收藏同一资源
          }

          // 投票
          if (!db.objectStoreNames.contains(STORES.VOTES)) {
            const voteStore = db.createObjectStore(STORES.VOTES, { keyPath: 'id', autoIncrement: true });
            voteStore.createIndex('createdBy', 'createdBy', { unique: false });
            voteStore.createIndex('status', 'status', { unique: false });
            voteStore.createIndex('createdAt', 'createdAt', { unique: false });
          }

          // 投票结果
          if (!db.objectStoreNames.contains(STORES.VOTE_RESULTS)) {
            const voteResultStore = db.createObjectStore(STORES.VOTE_RESULTS, { keyPath: 'id', autoIncrement: true });
            voteResultStore.createIndex('voteId', 'voteId', { unique: false });
            voteResultStore.createIndex('userId', 'userId', { unique: false });
            voteResultStore.createIndex('anonymous', 'anonymous', { unique: false });
          }

          // 笔记
          if (!db.objectStoreNames.contains(STORES.NOTES)) {
            const noteStore = db.createObjectStore(STORES.NOTES, { keyPath: 'id', autoIncrement: true });
            noteStore.createIndex('userId', 'userId', { unique: false });
            noteStore.createIndex('lessonId', 'lessonId', { unique: false });
            noteStore.createIndex('createdAt', 'createdAt', { unique: false });
          }

          // 问题
          if (!db.objectStoreNames.contains(STORES.QUESTIONS)) {
            const questionStore = db.createObjectStore(STORES.QUESTIONS, { keyPath: 'id', autoIncrement: true });
            questionStore.createIndex('userId', 'userId', { unique: false });
            questionStore.createIndex('answered', 'answered', { unique: false });
            questionStore.createIndex('time', 'time', { unique: false });
          }

          // 练习题
          if (!db.objectStoreNames.contains(STORES.EXERCISES)) {
            const exerciseStore = db.createObjectStore(STORES.EXERCISES, { keyPath: 'id', autoIncrement: true });
            exerciseStore.createIndex('type', 'type', { unique: false });
            exerciseStore.createIndex('difficulty', 'difficulty', { unique: false });
          }

          // 练习结果
          if (!db.objectStoreNames.contains(STORES.EXERCISE_RESULTS)) {
            const exerciseResultStore = db.createObjectStore(STORES.EXERCISE_RESULTS, { keyPath: 'id', autoIncrement: true });
            exerciseResultStore.createIndex('exerciseId', 'exerciseId', { unique: false });
            exerciseResultStore.createIndex('userId', 'userId', { unique: false });
            exerciseResultStore.createIndex('correct', 'correct', { unique: false });
            exerciseResultStore.createIndex('time', 'time', { unique: false });
          }

          // 用户资料
          if (!db.objectStoreNames.contains(STORES.USER_PROFILES)) {
            const userProfileStore = db.createObjectStore(STORES.USER_PROFILES, { keyPath: 'id' });
            userProfileStore.createIndex('role', 'role', { unique: false });
          }

          // 系统设置
          if (!db.objectStoreNames.contains(STORES.SETTINGS)) {
            const settingsStore = db.createObjectStore(STORES.SETTINGS, { keyPath: 'userId' });
          }

          // 新增题库存储
          if (!db.objectStoreNames.contains(STORES.QUIZ_QUESTIONS)) {
            const quizQuestionStore = db.createObjectStore(STORES.QUIZ_QUESTIONS, { keyPath: 'id', autoIncrement: true });
            quizQuestionStore.createIndex('type', 'type', { unique: false }); // 题目类型: 单选、多选、判断等
            quizQuestionStore.createIndex('subject', 'subject', { unique: false }); // 科目/主题分类
            quizQuestionStore.createIndex('difficulty', 'difficulty', { unique: false }); // 难度级别
            quizQuestionStore.createIndex('createdBy', 'createdBy', { unique: false }); // 创建者ID
            quizQuestionStore.createIndex('createdAt', 'createdAt', { unique: false }); // 创建时间
            quizQuestionStore.createIndex('status', 'status', { unique: false }); // 状态: 启用/禁用
          }

          // 新增学生答案存储
          if (!db.objectStoreNames.contains(STORES.QUIZ_ANSWERS)) {
            const quizAnswerStore = db.createObjectStore(STORES.QUIZ_ANSWERS, { keyPath: 'id', autoIncrement: true });
            quizAnswerStore.createIndex('questionId', 'questionId', { unique: false }); // 题目ID
            quizAnswerStore.createIndex('userId', 'userId', { unique: false }); // 学生ID
            quizAnswerStore.createIndex('isCorrect', 'isCorrect', { unique: false }); // 是否正确
            quizAnswerStore.createIndex('submittedAt', 'submittedAt', { unique: false }); // 提交时间
            quizAnswerStore.createIndex('examId', 'examId', { unique: false }); // 考试/测验ID
          }

          // 新增答题统计数据存储
          if (!db.objectStoreNames.contains(STORES.QUIZ_STATS)) {
            const quizStatsStore = db.createObjectStore(STORES.QUIZ_STATS, { keyPath: 'id', autoIncrement: true });
            quizStatsStore.createIndex('examId', 'examId', { unique: false }); // 考试/测验ID
            quizStatsStore.createIndex('questionId', 'questionId', { unique: false }); // 题目ID
            quizStatsStore.createIndex('correctRate', 'correctRate', { unique: false }); // 正确率
            quizStatsStore.createIndex('avgTime', 'avgTime', { unique: false }); // 平均答题时间
          }
          
          // 新增考试数据存储
          if (!db.objectStoreNames.contains(STORES.EXAMS)) {
            const examsStore = db.createObjectStore(STORES.EXAMS, { keyPath: 'id' }); // 使用自定义ID作为主键
            examsStore.createIndex('title', 'title', { unique: false }); // 考试标题
            examsStore.createIndex('createdBy', 'createdBy', { unique: false }); // 创建者ID
            examsStore.createIndex('createdAt', 'createdAt', { unique: false }); // 创建时间
            examsStore.createIndex('status', 'status', { unique: false }); // 考试状态
            examsStore.createIndex('startTime', 'startTime', { unique: false }); // 开始时间
          }
        },
        // 添加阻塞处理
        blocked() {
          console.warn('数据库升级被阻塞，可能有其他标签页打开了数据库');
          alert('请关闭所有其他标签页后重试');
        },
        // 添加阻塞其他标签页处理
        blocking() {
          console.warn('此连接正在阻塞其他标签页的数据库升级');
        },
        // 添加终止处理
        terminated() {
          console.error('数据库连接异常终止');
          dbInstance = null;
        }
      });
    } catch (openError) {
      console.error('打开数据库失败:', openError);
      
      // 删除原有的数据库并重新创建
      if (openError.name === 'VersionError' || openError.name === 'InvalidStateError') {
        console.warn('尝试删除并重建数据库...');
        await deleteDatabase();
        
        // 重新创建数据库
        dbInstance = await openDB(DB_NAME, DB_VERSION, {
          upgrade(db, oldVersion, newVersion, transaction) {
            console.log('重建数据库...', oldVersion, newVersion);
            
            // 资源库
            if (!db.objectStoreNames.contains(STORES.RESOURCES)) {
              const resourceStore = db.createObjectStore(STORES.RESOURCES, { keyPath: 'id', autoIncrement: true });
              resourceStore.createIndex('type', 'type', { unique: false });
              resourceStore.createIndex('subject', 'subject', { unique: false });
              resourceStore.createIndex('uploadTime', 'uploadTime', { unique: false });
            }
            
            // 资源评论
            if (!db.objectStoreNames.contains(STORES.RESOURCE_COMMENTS)) {
              const commentStore = db.createObjectStore(STORES.RESOURCE_COMMENTS, { keyPath: 'id', autoIncrement: true });
              commentStore.createIndex('resourceId', 'resourceId', { unique: false });
              commentStore.createIndex('userId', 'userId', { unique: false });
              commentStore.createIndex('time', 'time', { unique: false });
            }

            // 收藏功能
            if (!db.objectStoreNames.contains(STORES.FAVORITES)) {
              const favoriteStore = db.createObjectStore(STORES.FAVORITES, { keyPath: 'id', autoIncrement: true });
              favoriteStore.createIndex('resourceId', 'resourceId', { unique: false });
              favoriteStore.createIndex('userId', 'userId', { unique: false });
              favoriteStore.createIndex('createdAt', 'createdAt', { unique: false });
              favoriteStore.createIndex('userResource', ['userId', 'resourceId'], { unique: true });
            }
            
            // 投票
            if (!db.objectStoreNames.contains(STORES.VOTES)) {
              const voteStore = db.createObjectStore(STORES.VOTES, { keyPath: 'id', autoIncrement: true });
              voteStore.createIndex('createdBy', 'createdBy', { unique: false });
              voteStore.createIndex('status', 'status', { unique: false });
              voteStore.createIndex('createdAt', 'createdAt', { unique: false });
            }
            
            // 投票结果
            if (!db.objectStoreNames.contains(STORES.VOTE_RESULTS)) {
              const voteResultStore = db.createObjectStore(STORES.VOTE_RESULTS, { keyPath: 'id', autoIncrement: true });
              voteResultStore.createIndex('voteId', 'voteId', { unique: false });
              voteResultStore.createIndex('userId', 'userId', { unique: false });
              voteResultStore.createIndex('anonymous', 'anonymous', { unique: false });
            }
            
            // 笔记
            if (!db.objectStoreNames.contains(STORES.NOTES)) {
              const noteStore = db.createObjectStore(STORES.NOTES, { keyPath: 'id', autoIncrement: true });
              noteStore.createIndex('userId', 'userId', { unique: false });
              noteStore.createIndex('lessonId', 'lessonId', { unique: false });
              noteStore.createIndex('createdAt', 'createdAt', { unique: false });
            }
            
            // 问题
            if (!db.objectStoreNames.contains(STORES.QUESTIONS)) {
              const questionStore = db.createObjectStore(STORES.QUESTIONS, { keyPath: 'id', autoIncrement: true });
              questionStore.createIndex('userId', 'userId', { unique: false });
              questionStore.createIndex('answered', 'answered', { unique: false });
              questionStore.createIndex('time', 'time', { unique: false });
            }
            
            // 练习题
            if (!db.objectStoreNames.contains(STORES.EXERCISES)) {
              const exerciseStore = db.createObjectStore(STORES.EXERCISES, { keyPath: 'id', autoIncrement: true });
              exerciseStore.createIndex('type', 'type', { unique: false });
              exerciseStore.createIndex('difficulty', 'difficulty', { unique: false });
            }
            
            // 练习结果
            if (!db.objectStoreNames.contains(STORES.EXERCISE_RESULTS)) {
              const exerciseResultStore = db.createObjectStore(STORES.EXERCISE_RESULTS, { keyPath: 'id', autoIncrement: true });
              exerciseResultStore.createIndex('exerciseId', 'exerciseId', { unique: false });
              exerciseResultStore.createIndex('userId', 'userId', { unique: false });
              exerciseResultStore.createIndex('correct', 'correct', { unique: false });
              exerciseResultStore.createIndex('time', 'time', { unique: false });
            }
            
            // 用户资料
            if (!db.objectStoreNames.contains(STORES.USER_PROFILES)) {
              const userProfileStore = db.createObjectStore(STORES.USER_PROFILES, { keyPath: 'id' });
              userProfileStore.createIndex('role', 'role', { unique: false });
            }
            
            // 系统设置
            if (!db.objectStoreNames.contains(STORES.SETTINGS)) {
              const settingsStore = db.createObjectStore(STORES.SETTINGS, { keyPath: 'userId' });
            }
            
            // 新增题库存储
            if (!db.objectStoreNames.contains(STORES.QUIZ_QUESTIONS)) {
              const quizQuestionStore = db.createObjectStore(STORES.QUIZ_QUESTIONS, { keyPath: 'id', autoIncrement: true });
              quizQuestionStore.createIndex('type', 'type', { unique: false }); // 题目类型: 单选、多选、判断等
              quizQuestionStore.createIndex('subject', 'subject', { unique: false }); // 科目/主题分类
              quizQuestionStore.createIndex('difficulty', 'difficulty', { unique: false }); // 难度级别
              quizQuestionStore.createIndex('createdBy', 'createdBy', { unique: false }); // 创建者ID
              quizQuestionStore.createIndex('createdAt', 'createdAt', { unique: false }); // 创建时间
              quizQuestionStore.createIndex('status', 'status', { unique: false }); // 状态: 启用/禁用
            }
            
            // 新增学生答案存储
            if (!db.objectStoreNames.contains(STORES.QUIZ_ANSWERS)) {
              const quizAnswerStore = db.createObjectStore(STORES.QUIZ_ANSWERS, { keyPath: 'id', autoIncrement: true });
              quizAnswerStore.createIndex('questionId', 'questionId', { unique: false }); // 题目ID
              quizAnswerStore.createIndex('userId', 'userId', { unique: false }); // 学生ID
              quizAnswerStore.createIndex('isCorrect', 'isCorrect', { unique: false }); // 是否正确
              quizAnswerStore.createIndex('submittedAt', 'submittedAt', { unique: false }); // 提交时间
              quizAnswerStore.createIndex('examId', 'examId', { unique: false }); // 考试/测验ID
            }
            
            // 新增答题统计数据存储
            if (!db.objectStoreNames.contains(STORES.QUIZ_STATS)) {
              const quizStatsStore = db.createObjectStore(STORES.QUIZ_STATS, { keyPath: 'id', autoIncrement: true });
              quizStatsStore.createIndex('examId', 'examId', { unique: false }); // 考试/测验ID
              quizStatsStore.createIndex('questionId', 'questionId', { unique: false }); // 题目ID
              quizStatsStore.createIndex('correctRate', 'correctRate', { unique: false }); // 正确率
              quizStatsStore.createIndex('avgTime', 'avgTime', { unique: false }); // 平均答题时间
            }
            
            // 新增考试数据存储
            if (!db.objectStoreNames.contains(STORES.EXAMS)) {
              const examsStore = db.createObjectStore(STORES.EXAMS, { keyPath: 'id' }); // 使用自定义ID作为主键
              examsStore.createIndex('title', 'title', { unique: false }); // 考试标题
              examsStore.createIndex('createdBy', 'createdBy', { unique: false }); // 创建者ID
              examsStore.createIndex('createdAt', 'createdAt', { unique: false }); // 创建时间
              examsStore.createIndex('status', 'status', { unique: false }); // 考试状态
              examsStore.createIndex('startTime', 'startTime', { unique: false }); // 开始时间
            }
          }
        });
      } else {
        throw openError; // 其他错误则继续抛出
      }
    }

    console.log('数据库初始化成功');
    return dbInstance;
  } catch (error) {
    console.error('数据库初始化失败:', error);
    
    // 显示友好的错误消息
    try {
      alert(`数据库初始化失败: ${error.message || '未知错误'}\n请尝试清除浏览器缓存后重试`);
    } catch (e) {
      // 避免alert可能导致的错误
    }
    
    throw error;
  }
}

/**
 * 获取数据库实例
 * @returns {Promise<IDBDatabase>}
 */
export async function getDB() {
  if (!dbInstance) {
    return initDatabase();
  }
  return dbInstance;
}

/**
 * 删除数据库
 * @returns {Promise<void>}
 */
export async function deleteDatabase() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.deleteDatabase(DB_NAME);
    
    request.onsuccess = () => {
      console.log('成功删除数据库');
      dbInstance = null;
      resolve();
    };
    
    request.onerror = (event) => {
      console.error('删除数据库失败:', event);
      reject(new Error('删除数据库失败'));
    };
    
    request.onblocked = () => {
      console.warn('删除数据库被阻塞');
      alert('请关闭所有标签页后重试');
      reject(new Error('删除数据库被阻塞'));
    };
  });
}

/**
 * 重新初始化数据库
 * 当遇到"Object store not found"等错误时调用
 * @returns {Promise<IDBDatabase>}
 */
export async function reinitializeDatabase() {
  console.log('尝试重新初始化数据库...');
  try {
    // 先关闭现有连接
    if (dbInstance) {
      dbInstance.close();
      dbInstance = null;
    }
    
    // 删除现有数据库
    await deleteDatabase();
    
    // 重新初始化
    return initDatabase();
  } catch (error) {
    console.error('重新初始化数据库失败:', error);
    throw error;
  }
}

// 导出存储对象名称常量
export { STORES }; 