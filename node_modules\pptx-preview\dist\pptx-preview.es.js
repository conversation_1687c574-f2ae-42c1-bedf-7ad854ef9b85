import{__assign as t,__extends as e,__awaiter as a,__generator as r,__spreadArray as n}from"tslib";import o from"jszip";import{get as c,omit as i}from"lodash";import{v4 as s}from"uuid";import*as h from"echarts";var l=1;function p(t){var e="<".charCodeAt(0),a=">".charCodeAt(0),r="-".charCodeAt(0),n="/".charCodeAt(0),o="!".charCodeAt(0),c="'".charCodeAt(0),i='"'.charCodeAt(0),s="?".charCodeAt(0),h="\r\n\t>/= ",p=0;return l=1,d(function l(){for(var d=[];t[p];){if(t.charCodeAt(p)==e){if(t.charCodeAt(p+1)===n)return p=t.indexOf(">",p),d;if(t.charCodeAt(p+1)===o){if(t.charCodeAt(p+2)==r){for(;t.charCodeAt(p)!==a||t.charCodeAt(p-1)!=r||t.charCodeAt(p-2)!=r||-1==p;)p=t.indexOf(">",p+1);-1===p&&(p=t.length)}else for(p+=2;t.charCodeAt(p)!==a;p++);p++;continue}if(t.charCodeAt(p+1)===s){p=t.indexOf(">",p),p++;continue}for(var u=++p;-1===h.indexOf(t[p]);p++);for(var f=t.slice(u,p),y=!1,v={};t.charCodeAt(p)!==a;p++){var w=t.charCodeAt(p);if(w>64&&w<91||w>96&&w<123){for(u=p;-1===h.indexOf(t[p]);p++);for(var b=t.slice(u,p),m=t.charCodeAt(p);m!==c&&m!==i;)p++,m=t.charCodeAt(p);var g=t[p],x=++p;p=t.indexOf(g,x);var L=t.slice(x,p);y||(v={},y=!0),v[b]=L}}var M=[];t.charCodeAt(p-1)!==n&&(p++,M=l()),d.push({children:M,tagName:f,attrs:v})}else{var A=p;-2===(p=t.indexOf("<",p)-1)&&(p=t.length);var P=t.slice(A,p+1);P.length>0&&d.push(P)}p++}return d}())}function d(t){var e={};if(void 0===t)return{};if(1===t.length&&"string"==typeof t[0])return t[0];for(var a in t.forEach((function(t){if(e[t.tagName]||(e[t.tagName]=[]),"object"==typeof t){var a=d(t.children);"object"==typeof a&&(t.attrs&&(a.attrs=t.attrs),void 0===a.attrs?a.attrs={order:l}:a.attrs.order=l),l++,e[t.tagName].push(a)}})),e)1==e[a].length&&(e[a]=e[a][0]);return e}function u(t){return Math.abs(t)>2e4?"emu":"point"}function f(t){return t/12700}function y(t){return t/20}function v(t){return t/100}function w(t){return t/6e4}function b(t){return t/1e5}function m(t){var e=Math.ceil(t/26),a=(t%26||26)-1+65;return String.fromCharCode(a).repeat(e)}function g(t,e,a){var r={type:"solidFill"};if(t["a:srgbClr"])r.color="#"+t["a:srgbClr"].attrs.val;else if(t["a:schemeClr"]){var n=t["a:schemeClr"].attrs.val;a&&(n=a.getColorThemeName(n)),r.color=e.getColor(n)}else if(t["a:sysClr"])r.color="#"+t["a:sysClr"].attrs.lastClr;else if(t["a:prstClr"]){var o=c(t["a:prstClr"],["attrs","val"]);r.color=function(t){var e,a=["white","AliceBlue","AntiqueWhite","Aqua","Aquamarine","Azure","Beige","Bisque","black","BlanchedAlmond","Blue","BlueViolet","Brown","BurlyWood","CadetBlue","Chartreuse","Chocolate","Coral","CornflowerBlue","Cornsilk","Crimson","Cyan","DarkBlue","DarkCyan","DarkGoldenRod","DarkGray","DarkGrey","DarkGreen","DarkKhaki","DarkMagenta","DarkOliveGreen","DarkOrange","DarkOrchid","DarkRed","DarkSalmon","DarkSeaGreen","DarkSlateBlue","DarkSlateGray","DarkSlateGrey","DarkTurquoise","DarkViolet","DeepPink","DeepSkyBlue","DimGray","DimGrey","DodgerBlue","FireBrick","FloralWhite","ForestGreen","Fuchsia","Gainsboro","GhostWhite","Gold","GoldenRod","Gray","Grey","Green","GreenYellow","HoneyDew","HotPink","IndianRed","Indigo","Ivory","Khaki","Lavender","LavenderBlush","LawnGreen","LemonChiffon","LightBlue","LightCoral","LightCyan","LightGoldenRodYellow","LightGray","LightGrey","LightGreen","LightPink","LightSalmon","LightSeaGreen","LightSkyBlue","LightSlateGray","LightSlateGrey","LightSteelBlue","LightYellow","Lime","LimeGreen","Linen","Magenta","Maroon","MediumAquaMarine","MediumBlue","MediumOrchid","MediumPurple","MediumSeaGreen","MediumSlateBlue","MediumSpringGreen","MediumTurquoise","MediumVioletRed","MidnightBlue","MintCream","MistyRose","Moccasin","NavajoWhite","Navy","OldLace","Olive","OliveDrab","Orange","OrangeRed","Orchid","PaleGoldenRod","PaleGreen","PaleTurquoise","PaleVioletRed","PapayaWhip","PeachPuff","Peru","Pink","Plum","PowderBlue","Purple","RebeccaPurple","Red","RosyBrown","RoyalBlue","SaddleBrown","Salmon","SandyBrown","SeaGreen","SeaShell","Sienna","Silver","SkyBlue","SlateBlue","SlateGray","SlateGrey","Snow","SpringGreen","SteelBlue","Tan","Teal","Thistle","Tomato","Turquoise","Violet","Wheat","White","WhiteSmoke","Yellow","YellowGreen"],r=["ffffff","f0f8ff","faebd7","00ffff","7fffd4","f0ffff","f5f5dc","ffe4c4","000000","ffebcd","0000ff","8a2be2","a52a2a","deb887","5f9ea0","7fff00","d2691e","ff7f50","6495ed","fff8dc","dc143c","00ffff","00008b","008b8b","b8860b","a9a9a9","a9a9a9","006400","bdb76b","8b008b","556b2f","ff8c00","9932cc","8b0000","e9967a","8fbc8f","483d8b","2f4f4f","2f4f4f","00ced1","9400d3","ff1493","00bfff","696969","696969","1e90ff","b22222","fffaf0","228b22","ff00ff","dcdcdc","f8f8ff","ffd700","daa520","808080","808080","008000","adff2f","f0fff0","ff69b4","cd5c5c","4b0082","fffff0","f0e68c","e6e6fa","fff0f5","7cfc00","fffacd","add8e6","f08080","e0ffff","fafad2","d3d3d3","d3d3d3","90ee90","ffb6c1","ffa07a","20b2aa","87cefa","778899","778899","b0c4de","ffffe0","00ff00","32cd32","faf0e6","ff00ff","800000","66cdaa","0000cd","ba55d3","9370db","3cb371","7b68ee","00fa9a","48d1cc","c71585","191970","f5fffa","ffe4e1","ffe4b5","ffdead","000080","fdf5e6","808000","6b8e23","ffa500","ff4500","da70d6","eee8aa","98fb98","afeeee","db7093","ffefd5","ffdab9","cd853f","ffc0cb","dda0dd","b0e0e6","800080","663399","ff0000","bc8f8f","4169e1","8b4513","fa8072","f4a460","2e8b57","fff5ee","a0522d","c0c0c0","87ceeb","6a5acd","708090","708090","fffafa","00ff7f","4682b4","d2b48c","008080","d8bfd8","ff6347","40e0d0","ee82ee","f5deb3","ffffff","f5f5f5","ffff00","9acd32"],n=a.indexOf(t);-1!=n&&(e=r[n]);return"#".concat(e||"000000")}(o)}var i=t["a:srgbClr"]||t["a:schemeClr"]||t["a:sysClr"],s=c(i,["a:alpha","attrs","val"],1e5);r.alpha=s/1e5;var h=c(i,["a:shade","attrs","val"]);h&&(r.shade=h/1e5);var l=c(i,["a:lumMod","attrs","val"]);l&&(r.lumMod=l/1e5);var p=c(i,["a:lumOff","attrs","val"]);p&&(r.lumOff=p/1e5);var d=c(i,["a:tint","attrs","val"]);return d&&(r.tint=d/1e5),r}function x(t,e,a){var r,n={type:"blipFill"},o=c(t,["a:blip","attrs","r:embed"]);if(o){var i=null===(r=a.rels[o])||void 0===r?void 0:r.target;i&&(n.base64=e.getMedia(i))}var s=c(t,["a:blip","a:alphaModFix","attrs","amt"]);s&&(n.alpha=s/1e5);var h=c(t,["a:stretch","a:fillRect","attrs"]);return h&&(n.fillRect={},h.b&&(n.fillRect.b=h.b/1e5),h.t&&(n.fillRect.t=h.t/1e5),h.r&&(n.fillRect.r=h.r/1e5),h.l&&(n.fillRect.l=h.l/1e5)),n}function L(t,e,a){var r={type:"gradFill",tileRect:{},lin:{},gsList:[]};r.flip=t.attrs.flip,r.path=c(t,["a:path","attrs","path"])||"linear",r.rotWithShape="1"===t.attrs.rotWithShape,c(t,["a:lin","attrs","ang"])&&(r.lin.ang=w(t["a:lin"].attrs.ang)),c(t,["a:lin","attrs","scaled"])&&(r.lin.scaled="1"===t["a:lin"].attrs.scaled);var n=c(t,["a:gsLst","a:gs"])||[];return r.gsList=n.map((function(t){return{color:g(t,e,a),pos:b(t.attrs.pos)}})),c(t,["a:tileRect","attrs","l"])&&(r.tileRect.l=b(t["a:tileRect"].attrs.l)),c(t,["a:tileRect","attrs","t"])&&(r.tileRect.t=b(t["a:tileRect"].attrs.t)),c(t,["a:tileRect","attrs","r"])&&(r.tileRect.r=b(t["a:tileRect"].attrs.r)),c(t,["a:tileRect","attrs","b"])&&(r.tileRect.b=b(t["a:tileRect"].attrs.b)),r}function M(t){return t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function A(t){return t<.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055}function P(t,e){var a=t[0],r=t[1],n=t[2],o=M(a/255)*e,c=M(r/255)*e,i=M(n/255)*e;return[Math.round(255*A(o)),Math.round(255*A(c)),Math.round(255*A(i))]}function k(t,e,a){var r=t/255,n=e/255,o=a/255,c=Math.max(r,n,o),i=Math.min(r,n,o),s=c-i,h=0,l=(c+i)/2;return 0===s?h=0:c===r?h=(n-o)/s%6:c===n?h=(o-r)/s+2:c===o&&(h=(r-n)/s+4),(h=Math.round(60*h))<0&&(h+=360),{h:h,s:0===s||0===l||1===l?0:s/(1-Math.abs(2*l-1)),l:l}}function j(t,e,a){var r,n,o,c=(1-Math.abs(2*a-1))*e,i=c*(1-Math.abs(t/60%2-1)),s=a-c/2;return t<60?(r=c,n=i,o=0):t<120?(r=i,n=c,o=0):t<180?(r=0,n=c,o=i):t<240?(r=0,n=i,o=c):t<300?(r=i,n=0,o=c):(r=c,n=0,o=i),[r=Math.round(255*(r+s)),n=Math.round(255*(n+s)),o=Math.round(255*(o+s))]}function S(t,e){if(!t||"none"===t.type)return"";if("solidFill"===t.type&&/^#[\da-fA-F]{3,6}$/.test(t.color)){var a=parseInt(t.color.substr(1,2),16),r=parseInt(t.color.substr(3,2),16),n=parseInt(t.color.substr(5,2),16);if(t.shade){var o=P([a,r,n],t.shade);a=o[0],r=o[1],n=o[2]}if(t.lumMod){var c=function(t,e,a,r){var n=k(t,e,a),o=n.h,c=n.l*r;return c>=1&&(c=1),j(o,n.s,c)}(a,r,n,t.lumMod);a=c[0],r=c[1],n=c[2]}if(t.lumOff){c=function(t,e,a,r){var n=k(t,e,a),o=n.h,c=r+n.l;return c>1&&(c=1),j(o,n.s,c)}(a,r,n,t.lumOff);a=c[0],r=c[1],n=c[2]}if(t.tint||(null==e?void 0:e.light)){var i=function(t,e,a,r){var n=k(t,e,a),o=n.h,c=n.l;return r>=1&&(r=1),j(o,n.s,c*r+(1-r))}(a,r,n,t.tint||(null==e?void 0:e.light));a=i[0],r=i[1],n=i[2]}if(null==e?void 0:e.dark){var s=P([a,r,n],null==e?void 0:e.dark);a=s[0],r=s[1],n=s[2]}var h=t.alpha;return"rgba(".concat(a,",").concat(r,",").concat(n,",").concat(h,")")}}var C=function(){function t(t,e,a){var r,n;this.uuid=s(),this.offset={x:0,y:0},this.extend={w:0,h:0},this.rotate=0,this.order=0,this.flipV=!1,this.flipH=!1,this.source=t,this.ctx=e,this.group=a;var o=c(t,["p:nvSpPr","p:nvPr"]);if(o){var i=c(o,"p:ph");i&&i.attrs&&(this.idx=i.attrs.idx,this.type=i.attrs.type),c(o,["attrs","userDrawn"])&&(this.userDrawn="1"===c(o,["attrs","userDrawn"]))}if(this.order=c(t,"attrs.order",0),this.source["p:spPr"]){if(l=this.getXfrm()){var h=this.group&&((null===(r=this.ctx.pptx)||void 0===r?void 0:r.wps)||"point"===u(parseInt(l["a:off"].attrs.x)))?y:f;this.offset={x:Math.round(h(parseInt(l["a:off"].attrs.x))),y:Math.round(h(parseInt(l["a:off"].attrs.y)))},this.extend={w:Math.round(h(parseInt(l["a:ext"].attrs.cx))),h:Math.round(h(parseInt(l["a:ext"].attrs.cy||"0")))},this.rotate=w(parseInt(c(l,"attrs.rot",0))),this.flipV="1"===c(l,"attrs.flipV"),this.flipH="1"===c(l,"attrs.flipH")}}else if(this.source["p:xfrm"]){var l=this.source["p:xfrm"];h=this.group&&((null===(n=this.ctx.pptx)||void 0===n?void 0:n.wps)||"point"===u(parseInt(l["a:off"].attrs.x)))?y:f;this.offset={x:Math.round(h(parseInt(l["a:off"].attrs.x))),y:Math.round(h(parseInt(l["a:off"].attrs.y)))},this.extend={w:Math.round(h(parseInt(l["a:ext"].attrs.cx))),h:Math.round(h(parseInt(l["a:ext"].attrs.cy)))}}if(a){var p=a.extend,d=a.chExtend,v=a.chOffset,b=0===d.w?0:p.w/d.w,m=0===d.h?0:p.h/d.h;this.extend.w=this.extend.w*b,this.extend.h=this.extend.h*m,this.offset.x=(this.offset.x-v.x)*b,this.offset.y=(this.offset.y-v.y)*m}}return Object.defineProperty(t.prototype,"theme",{get:function(){return(this.ctx.sliderMaster||this.ctx).theme},enumerable:!1,configurable:!0}),t.prototype.getColorThemeName=function(t){return this.ctx.getColorThemeName(t)},t.prototype.getXfrm=function(){var t=this.source["p:spPr"]["a:xfrm"];return t||(this.idx?t=this.ctx.getNodeInheritAttrsByIdx(this.idx,["p:spPr","a:xfrm"]):this.type&&(t=this.ctx.getNodeInheritAttrsByType(this.type,["p:spPr","a:xfrm"]))),t},t}(),I=function(){function e(t,e){this.props={},this.inheritProps={},this.source=t,this.node=e,this._getInheritBodyProps(),this._parseBodyProps(),this._parseLstStyle(),this._parseText()}return e.prototype._getInheritBodyProps=function(){var t,e=this.node.ctx,a=this.node.type,r=this.node.idx;if(a||r)switch(e.slideType){case"slideMaster":break;case"slideLayout":(t=a?e.slideMaster.getNodeByType(a):e.slideMaster.getNodeByIdx(r))&&(this.inheritProps=c(t,["textBody","props"])||{});break;case"slide":(t=a?e.slideLayout.slideMaster.getNodeByType(a):e.slideLayout.slideMaster.getNodeByIdx(r))&&Object.assign(this.inheritProps,c(t,["textBody","props"])||{}),(t=a?e.slideLayout.getNodeByType(a):e.slideLayout.getNodeByIdx(r))&&Object.assign(this.inheritProps,c(t,["textBody","props"])||{})}},e.prototype._parseBodyProps=function(){var t=this,e=c(this.source,["a:bodyPr","attrs"])||{};Object.keys(e).forEach((function(a){switch(a){case"anchor":t.props.anchor=e[a];break;case"rtlCol":t.props.rtlCol="1"===e[a];break;case"lIns":case"rIns":case"tIns":case"bIns":t.props[a]=f(parseInt(e[a]));break;case"order":break;default:t.props[a]=e[a]}}));var a=c(this.source,["a:bodyPr","a:normAutofit","attrs"]);if(a){this.props.normAutofit={};var r=a.fontScale;r&&(this.props.normAutofit.fontScale=b(parseInt(r)));var n=a.lnSpcReduction;n&&(this.props.normAutofit.lnSpcReduction=b(parseInt(n)))}},e.prototype._parseLstStyle=function(){var t=this,e={},a=c(this.source,"a:lstStyle")||{};Object.keys(a).forEach((function(r){if(r.startsWith("a:")&&r.endsWith("pPr")){var n=r.substr(2,r.length-5);e[n]={props:t._formatPPr(a[r])};var o=c(a[r],["a:defRPr"]);e[n].defRPr=t._formatRPr(o)}})),this.lstStyle=e},e.prototype._parseText=function(){var t=this,e=c(this.source,["a:p"])||[];Array.isArray(e)||(e=[e]),this.paragraphs=e.map((function(e){return t._parseParagraph(e)}))},e.prototype._parseParagraph=function(e){var a=this,r={props:{},inheritProps:{},inheritRProps:{},endParaRProps:{},rows:[]},n=c(e,["a:pPr"])||{};r.props=this._formatPPr(n);var o=c(e,["a:endParaRPr"]);r.endParaRProps=this._formatRPr(o);var i=c(e,["a:r"])||[];Array.isArray(i)||(i=[i]);var s=c(e,["a:br"])||[];return Array.isArray(s)||(s=[s]),(i=i.concat(s.map((function(e){return t({isBr:!0},e)})))).sort((function(t,e){return c(t,["attrs","order"])-c(e,["attrs","order"])})),r.rows=i.map((function(t){return a._parseRow(t)})),r.inheritProps=this._getInheritPProps(r.props.level),r.inheritRProps=this._getInheritRProps(r.props.level),r},e.prototype._getInheritPProps=function(t){void 0===t&&(t="0");var e,a={},r=this.node.ctx,n=this.node.type,o=this.node.idx;switch(r.slideType){case"slideMaster":this.node.isTextBox?Object.assign(a,c(r.defaultTextStyle,["lvl".concat(t?+t+1:1),"props"])||{}):Object.assign(a,c(r,["textStyles","otherStyle","lvl".concat(t?+t+1:1),"props"])||{});break;case"slideLayout":this.node.isTextBox?Object.assign(a,c(r.slideMaster.defaultTextStyle,["lvl".concat(t?+t+1:1),"props"])||{}):Object.assign(a,c(r.slideMaster,["textStyles","otherStyle","lvl".concat(t?+t+1:1),"props"])||{}),(n||o)&&(e=n?r.slideMaster.getNodeByType(n):r.slideMaster.getNodeByIdx(o))&&Object.assign(a,c(e,["textBody","lstStyle","lvl".concat(t?+t+1:1),"props"])||{});break;case"slide":this.node.isTextBox?Object.assign(a,c(r.slideLayout.slideMaster.defaultTextStyle,["lvl".concat(t?+t+1:1),"props"])||{}):Object.assign(a,c(r.slideLayout.slideMaster,["textStyles","otherStyle","lvl".concat(t?+t+1:1),"props"])||{}),(n||o)&&(["subTitle","ctrTitle","title"].includes(n)&&Object.assign(a,c(r.slideLayout.slideMaster,["textStyles","titleStyle","lvl".concat(t?+t+1:1),"props"])||{}),(e=n?r.slideLayout.slideMaster.getNodeByType(n):r.slideLayout.slideMaster.getNodeByIdx(o))&&Object.assign(a,c(e,["textBody","lstStyle","lvl".concat(t?+t+1:1),"props"])||{}),(e=n?r.slideLayout.getNodeByType(n):r.slideLayout.getNodeByIdx(o))&&Object.assign(a,c(e,["textBody","lstStyle","lvl".concat(t?+t+1:1),"props"])||{}))}return a},e.prototype._getInheritRProps=function(t){void 0===t&&(t="0");var e,a={},r=this.node.ctx,n=this.node.type,o=this.node.idx;switch(r.slideType){case"slideMaster":this.node.isTextBox?Object.assign(a,c(r.defaultTextStyle,["lvl".concat(t?+t+1:1),"defRPr"])||{}):Object.assign(a,c(r,["textStyles","otherStyle","lvl".concat(t?+t+1:1),"defRPr"])||{});break;case"slideLayout":this.node.isTextBox?Object.assign(a,c(r.slideMaster.defaultTextStyle,["lvl".concat(t?+t+1:1),"defRPr"])||{}):Object.assign(a,c(r.slideMaster,["textStyles","otherStyle","lvl".concat(t?+t+1:1),"defRPr"])||{}),(n||o)&&(e=n?r.slideMaster.getNodeByType(n):r.slideMaster.getNodeByIdx(o))&&(a=c(e,["textBody","lstStyle","lvl".concat(t?+t+1:1),"defRPr"])||{});break;case"slide":this.node.isTextBox?Object.assign(a,c(r.slideLayout.slideMaster.defaultTextStyle,["lvl".concat(t?+t+1:1),"defRPr"])||{}):Object.assign(a,c(r.slideLayout.slideMaster,["textStyles","otherStyle","lvl".concat(t?+t+1:1),"defRPr"])||{}),(n||o)&&(["subTitle","ctrTitle","title"].includes(n)&&Object.assign(a,c(r.slideLayout.slideMaster,["textStyles","titleStyle","lvl".concat(t?+t+1:1),"defRPr"])||{}),(e=n?r.slideLayout.slideMaster.getNodeByType(n):r.slideLayout.slideMaster.getNodeByIdx(o))&&Object.assign(a,c(e,["textBody","lstStyle","lvl".concat(t?+t+1:1),"defRPr"])||{}),(e=n?r.slideLayout.getNodeByType(n):r.slideLayout.getNodeByIdx(o))&&Object.assign(a,c(e,["textBody","lstStyle","lvl".concat(t?+t+1:1),"defRPr"])||{}))}var i=c(this.node.source,["p:style","a:fontRef"]);return c(i,"a:schemeClr")&&(a.color=g(i,this.node.theme,this.node)),c(this.lstStyle,["lvl".concat(t?+t+1:1),"defRPr"])&&Object.assign(a,c(this.lstStyle,["lvl".concat(t?+t+1:1),"defRPr"])),a},e.prototype._formatPPr=function(t){var e={},a=c(t,"attrs")||{};return Object.keys(a).forEach((function(t){switch(t){case"algn":e.align=a[t];break;case"marL":e.marginLeft=f(parseInt(a[t]));break;case"indent":e.indent=f(parseInt(a[t]));break;case"lvl":e.level=a[t]}})),c(t,["a:lnSpc","a:spcPct","attrs","val"])&&(e.lineHeight=parseInt(t["a:lnSpc"]["a:spcPct"].attrs.val)/1e5),c(t,["a:buAutoNum","attrs","type"])&&(e.buAutoNum=t["a:buAutoNum"].attrs.type),c(t,["a:buChar","attrs","char"])&&(e.buChar=t["a:buChar"].attrs.char),c(t,["a:spcBef","a:spcPts","attrs","val"])&&(e.spaceBefore=v(parseInt(t["a:spcBef"]["a:spcPts"].attrs.val))),c(t,["a:spcAft","a:spcPts","attrs","val"])&&(e.spaceAfter=v(parseInt(t["a:spcAft"]["a:spcPts"].attrs.val))),e},e.prototype._parseRow=function(t){if(t.isBr)return{isBr:!0};var e={props:{},text:""},a=c(t,["a:rPr"])||{};return e.props=this._formatRPr(a),e.text=c(t,"a:t")||"",e},e.prototype._formatRPr=function(t){var e={},a=c(t,"attrs")||{};Object.keys(a).forEach((function(t){switch(t){case"sz":e.size=parseInt(a[t])/100;break;case"b":e.bold="1"===a[t];break;case"i":e.italic="1"===a[t];break;case"u":e.underline=a[t];break;case"strike":e.strike=a[t];break;case"order":case"dirty":break;default:e[t]=a[t]}}));var r=c(t,"a:solidFill");r&&(e.color=g(r,this.node.theme,this.node));var n=c(t,"a:highlight");return n&&(e.background=g(n,this.node.theme,this.node)),e.typeface=c(t,["a:ea","attrs","typeface"]),e},e}();function B(t,e,a){var r={};if(!c(t,"a:noFill")){c(t,"attrs.w")&&(r.width=f(parseInt(c(t,"attrs.w"))));var n=c(t,"a:solidFill");n&&(r.color=g(n,e,a));var o=c(t,"a:prstDash");if(o&&(r.type=o.attrs.val),c(t,["a:miter"])&&(r.lineJoin="miter"),c(t,["a:bevel"])&&(r.lineJoin="bevel"),c(t,["a:round"])&&(r.lineJoin="round"),c(t,["a:miter","attrs","lim"])&&(r.miterLim=f(parseInt(c(t,["a:miter","attrs","lim"])))),c(t,["a:headEnd"])){var i=c(t,["a:headEnd","attrs"]);r.headEnd={type:i.type,len:i.len,w:i.w}}if(c(t,["a:tailEnd"])){var s=c(t,["a:tailEnd","attrs"]);r.tailEnd={type:s.type,len:s.len,w:s.w}}return r}}var T=function(a){function r(t,e,r,n){var o=a.call(this,t,r,n)||this;return o.border={},o.prstGeom={},o.isTextBox=!1,o.pptx=e,o._parseShape(),o._parIsTextBox(),o._parsePrstGeom(),o._parseBackground(),o._parseBorder(),o._parseTxt(),o}return e(r,a),r.prototype._parseShape=function(){if(this.shape=c(this.source,["p:spPr","a:prstGeom","attrs","prst"]),!this.shape&&c(this.source,["p:spPr","a:custGeom"])){this.shape="customGeom";var t=c(this.source,["p:spPr","a:custGeom","a:pathLst","a:path"]),e=[],a=[],r=function(r){switch(r){case"a:moveTo":case"a:cubicBezTo":case"a:lnTo":a=Array.isArray(t[r])?t[r]:[t[r]],e=e.concat(a.map((function(t){return{order:t.attrs.order,type:r.split(":")[1],points:(Array.isArray(t["a:pt"])?t["a:pt"]:[t["a:pt"]]).map((function(t){return[f(parseInt(c(t,["attrs","x"]))),f(parseInt(c(t,["attrs","y"])))]}))}})));break;case"a:close":a=Array.isArray(t[r])?t[r]:[t[r]],e=e.concat(a.map((function(t){return{order:t.attrs.order,type:r.split(":")[1]}})))}};for(var n in t)r(n);e.sort((function(t,e){return t.order-e.order})),this.prstGeom.pathList=e,c(t,["attrs","w"])&&(this.prstGeom.w=f(parseInt(c(t,["attrs","w"])))),c(t,["attrs","h"])&&(this.prstGeom.h=f(parseInt(c(t,["attrs","h"]))))}},r.prototype._parIsTextBox=function(){this.isTextBox="1"===c(this.source,["p:nvSpPr","p:cNvSpPr","attrs","txBox"])},r.prototype._parsePrstGeom=function(){var t=this,e=c(this.source,["p:spPr","a:prstGeom"]),a=c(e,["a:avLst","a:gd"]);a&&(Array.isArray(a)||(a=[a]),this.prstGeom.gd=a.map((function(e){var a=["pie","chord","arc"].includes(t.shape)||["blockArc"].includes(t.shape)&&["adj1","adj2"].includes(e.attrs.name)?w(parseInt(e.attrs.fmla.split(" ")[1])):b(parseInt(e.attrs.fmla.split(" ")[1]));return{name:e.attrs.name,fmla:a}})))},r.prototype._parseBackground=function(){if(!c(this.source,["p:spPr","a:noFill"]))if(c(this.source,["p:spPr","a:grpFill"])&&this.group)this.background=this.group.getBackground();else{var t=c(this.source,["p:spPr","a:solidFill"]);if(t)this.background=g(t,this.theme,this);else{var e=c(this.source,["p:spPr","a:gradFill"]);if(e)this.background=L(e,this.theme,this);else{var a=c(this.source,["p:spPr","a:blipFill"]);if(a)this.background=x(a,this.pptx,this.ctx);else{var r=c(this.source,["p:style","a:fillRef"]);r&&(this.background=g(r,this.theme,this))}}}}},r.prototype._parseBorder=function(){var e=c(this.source,["p:style","a:lnRef"]);if(e){var a=parseInt(e.attrs.idx),n=this.theme.getLineStyle(a);this.border=t(t({},n),this.border),this.border.color&&this.border.color.color||(this.border.color=g(e,this.theme,this))}var o=c(this.source,["p:spPr","a:ln"]);o&&Object.assign(this.border,B(o,this.theme,this)),this.border.color&&this.border.color.color&&!this.border.width&&(this.border.width=r.defaultBorderWidth)},r.prototype._parseTxt=function(){this.textBody=new I(c(this.source,["p:txBody"]),this)},r.defaultBorderWidth=.75,r}(C),R=function(t){function a(e,a,r,n,o){var i,s,h=t.call(this,a,n,o)||this;h.userDrawn=!0,h.pptx=r,h.path=e;var l=c(h.source,["p:blipFill","a:srcRect"]);l&&(h.clip={},l.attrs.b&&(h.clip.b=parseInt(l.attrs.b)/1e5),l.attrs.t&&(h.clip.t=parseInt(l.attrs.t)/1e5),l.attrs.l&&(h.clip.l=parseInt(l.attrs.l)/1e5),l.attrs.r&&(h.clip.r=parseInt(l.attrs.r)/1e5));var p=c(a,["p:nvPicPr","p:nvPr","a:audioFile","attrs","r:link"]);if(p){var d=null===(i=h.ctx.rels[p])||void 0===i?void 0:i.target;h.audioFile=h.pptx.getMedia(d)}var u=c(a,["p:nvPicPr","p:nvPr","a:videoFile","attrs","r:link"]);if(u){var f=null===(s=h.ctx.rels[u])||void 0===s?void 0:s.target;h.videoFile=h.pptx.getMedia(f)}return h}return e(a,t),Object.defineProperty(a.prototype,"base64",{get:function(){return this.pptx.getMedia(this.path)},enumerable:!1,configurable:!0}),a}(C),_=function(a){function r(t,e,r,n){var o=a.call(this,t,r,n)||this;return o.userDrawn=!0,o.props={tableStyleId:""},o.tableGrid={gridCol:[]},o.tr=[],o.tableStyles={},o.pptx=e,o._parseTableProps(),o._parseTableGrid(),o._parseTr(),o._parseInheritStyles(),o}return e(r,a),Object.defineProperty(r.prototype,"slideMaster",{get:function(){return this.ctx.slideMaster||this.ctx},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"theme",{get:function(){return this.slideMaster.theme},enumerable:!1,configurable:!0}),r.prototype._parseTableProps=function(){var t=c(this.source,["a:graphic","a:graphicData","a:tbl","a:tblPr"]);this.props.tableStyleId=c(t,"a:tableStyleId"),this.tableStyles=c(this.slideMaster.tableStyles,this.props.tableStyleId)||{},"1"===c(t,["attrs","bandCol"])&&(this.props.bandCol=!0),"1"===c(t,["attrs","bandRow"])&&(this.props.bandRow=!0),"1"===c(t,["attrs","firstCol"])&&(this.props.firstCol=!0),"1"===c(t,["attrs","firstRow"])&&(this.props.firstRow=!0),"1"===c(t,["attrs","lastCol"])&&(this.props.lastCol=!0),"1"===c(t,["attrs","lastRow"])&&(this.props.lastRow=!0)},r.prototype._parseTableGrid=function(){var t=c(this.source,["a:graphic","a:graphicData","a:tbl","a:tblGrid","a:gridCol"]);if(t)for(var e=0;e<t.length;e++){var a=c(t[e],["attrs","w"]);this.tableGrid.gridCol.push({width:f(parseInt(a))})}},r.prototype._parseTr=function(){var t=[],e=c(this.source,["a:graphic","a:graphicData","a:tbl","a:tr"]);Array.isArray(e)||(e=[e]);for(var a=0;a<e.length;a++){var r={props:{},td:[]},n=e[a];r.props.height=f(parseInt(c(n,["attrs","h"])));var o=c(n,["a:tc"]);Array.isArray(o)||(o=[o]);for(var i=0;i<o.length;i++)r.td.push(this._parseTd(o[i]));t.push(r)}this.tr=t},r.prototype._parseTd=function(t){var e,a,r,n,o=this,i={props:{border:{}},paragraphs:[]},s=c(t,["a:tcPr","attrs"]);(null==s?void 0:s.marB)&&(i.props.marB=f(parseInt(null==s?void 0:s.marB))),(null==s?void 0:s.marT)&&(i.props.marT=f(parseInt(null==s?void 0:s.marT))),(null==s?void 0:s.marL)&&(i.props.marL=f(parseInt(null==s?void 0:s.marL))),(null==s?void 0:s.marR)&&(i.props.marR=f(parseInt(null==s?void 0:s.marR))),(null==s?void 0:s.anchor)&&(i.props.anchor=null==s?void 0:s.anchor);var h=c(t,["a:tcPr"]);c(h,["a:lnR"])&&(i.props.border.right=B(c(h,["a:lnR"]),this.theme,this.ctx)),c(h,["a:lnL"])&&(i.props.border.left=B(c(h,["a:lnL"]),this.theme,this.ctx)),c(h,["a:lnT"])&&(i.props.border.top=B(c(h,["a:lnT"]),this.theme,this.ctx)),c(h,["a:lnB"])&&(i.props.border.bottom=B(c(h,["a:lnB"]),this.theme,this.ctx)),(null===(e=null==t?void 0:t.attrs)||void 0===e?void 0:e.rowSpan)&&(i.props.rowSpan=parseInt(t.attrs.rowSpan)),(null===(a=null==t?void 0:t.attrs)||void 0===a?void 0:a.gridSpan)&&(i.props.gridSpan=parseInt(t.attrs.gridSpan)),(null===(r=null==t?void 0:t.attrs)||void 0===r?void 0:r.vMerge)&&(i.props.vMerge="1"===t.attrs.vMerge),(null===(n=null==t?void 0:t.attrs)||void 0===n?void 0:n.hMerge)&&(i.props.hMerge="1"===t.attrs.hMerge);var l=c(t,["a:tcPr","a:solidFill"]);l&&(i.props.background=g(l,this.theme,this.ctx));var p=c(t,["a:txBody"]),d=c(p,["a:p"]);return Array.isArray(d)||(d=[d]),i.paragraphs=d.map((function(t){return o._parseParagraph(t)})),i},r.prototype._parseParagraph=function(e){var a=this,r={props:{},inheritProps:{},inheritRProps:{},endParaRProps:{},rows:[]},n=c(e,["a:pPr"])||{};r.props=this._formatPPr(n);var o=c(e,["a:endParaRPr"]);r.endParaRProps=this._formatRPr(o);var i=c(e,["a:r"])||[];Array.isArray(i)||(i=[i]);var s=c(e,["a:br"])||[];return Array.isArray(s)||(s=[s]),(i=i.concat(s.map((function(e){return t({isBr:!0},e)})))).sort((function(t,e){return c(t,["attrs","order"])-c(e,["attrs","order"])})),r.rows=i.map((function(t){return a._parseRow(t)})),r},r.prototype._parseRow=function(t){if(t.isBr)return{isBr:!0};var e={props:{},text:""},a=c(t,["a:rPr"])||{};return e.props=this._formatRPr(a),e.text=c(t,"a:t")||"",e},r.prototype._formatPPr=function(t){var e={},a=c(t,"attrs")||{};return Object.keys(a).forEach((function(t){if("algn"===t)e.align=a[t]})),c(t,["a:lnSpc","a:spcPct","attrs","val"])&&(e.lineHeight=parseInt(t["a:lnSpc"]["a:spcPct"].attrs.val)/1e5),c(t,["a:buAutoNum","attrs","type"])&&(e.buAutoNum=t["a:buAutoNum"].attrs.type),c(t,["a:buChar","attrs","char"])&&(e.buChar=t["a:buChar"].attrs.char),c(t,["a:spcBef","a:spcPts","attrs","val"])&&(e.spaceBefore=v(parseInt(t["a:spcBef"]["a:spcPts"].attrs.val))),c(t,["a:spcAft","a:spcPts","attrs","val"])&&(e.spaceAfter=v(parseInt(t["a:spcAft"]["a:spcPts"].attrs.val))),e},r.prototype._formatRPr=function(t){var e={},a=c(t,"attrs")||{};Object.keys(a).forEach((function(t){switch(t){case"sz":e.size=parseInt(a[t])/100;break;case"b":e.bold="1"===a[t];break;case"i":e.italic="1"===a[t];break;case"u":e.underline=a[t];break;case"strike":e.strike=a[t];break;case"order":case"dirty":break;default:e[t]=a[t]}}));var r=c(t,"a:solidFill");r&&(e.color=g(r,this.theme,this.ctx));var n=c(t,"a:highlight");return n&&(e.background=g(n,this.theme,this.ctx)),e.typeface=c(t,["a:ea","attrs","typeface"]),e},r.prototype._isLastCol=function(t,e){var a,r;if(e===t.length-1)return!0;for(var n=e+1;n<t.length;n++)if(!(null===(a=t[n].props)||void 0===a?void 0:a.hMerge)&&!(null===(r=t[n].props)||void 0===r?void 0:r.vMerge))return!1;return!0},r.prototype._isBandRow=function(t){var e;return(null===(e=this.props)||void 0===e?void 0:e.firstRow)?t%2==1:t%2==0},r.prototype._isBandCol=function(t){var e;return(null===(e=this.props)||void 0===e?void 0:e.firstCol)?t%2==1:t%2==0},r.prototype._parseInheritStyles=function(){var e,a,r,n,o,c,i,s,h=this,l=null===(a=null===(e=this.tableStyles)||void 0===e?void 0:e.wholeTbl)||void 0===a?void 0:a.tcStyle,p=null===(n=null===(r=this.tableStyles)||void 0===r?void 0:r.wholeTbl)||void 0===n?void 0:n.tcTxStyle,d=null===(c=null===(o=this.slideMaster.defaultTextStyle)||void 0===o?void 0:o.lvl1)||void 0===c?void 0:c.props,u=null===(s=null===(i=this.slideMaster.defaultTextStyle)||void 0===i?void 0:i.lvl1)||void 0===s?void 0:s.defRPr;this.tr.forEach((function(e,a){e.td.forEach((function(r,n){var o,c,i,s,f,y,v,w,b,m,g,x,L,M,A,P,k,j,S,C,I,B,T,R,_,O,E,N,D,F,Z,G,z,W,H,Q,V,X,q,U,Y,J,K,$,tt,et,at,rt,nt,ot,ct,it,st,ht,lt,pt,dt=t(t({},d),l),ut=t(t({},u),p);h.props.firstRow&&0===a?(dt=t(t(t({},dt),null===(c=null===(o=h.tableStyles)||void 0===o?void 0:o.firstRow)||void 0===c?void 0:c.tcStyle),{border:t(t({},null==dt?void 0:dt.border),null===(f=null===(s=null===(i=h.tableStyles)||void 0===i?void 0:i.firstRow)||void 0===s?void 0:s.tcStyle)||void 0===f?void 0:f.border)}),ut=t(t({},ut),null===(v=null===(y=h.tableStyles)||void 0===y?void 0:y.firstRow)||void 0===v?void 0:v.tcTxStyle)):h.props.lastRow&&a===h.tr.length-1?(dt=t(t(t({},dt),null===(b=null===(w=h.tableStyles)||void 0===w?void 0:w.lastRow)||void 0===b?void 0:b.tcStyle),{border:t(t({},null==dt?void 0:dt.border),null===(x=null===(g=null===(m=h.tableStyles)||void 0===m?void 0:m.lastRow)||void 0===g?void 0:g.tcStyle)||void 0===x?void 0:x.border)}),ut=t(t({},ut),null===(M=null===(L=h.tableStyles)||void 0===L?void 0:L.lastRow)||void 0===M?void 0:M.tcTxStyle)):h.props.firstCol&&0===n?(dt=t(t(t({},dt),null===(P=null===(A=h.tableStyles)||void 0===A?void 0:A.firstCol)||void 0===P?void 0:P.tcStyle),{border:t(t({},null==dt?void 0:dt.border),null===(S=null===(j=null===(k=h.tableStyles)||void 0===k?void 0:k.firstCol)||void 0===j?void 0:j.tcStyle)||void 0===S?void 0:S.border)}),ut=t(t({},ut),null===(I=null===(C=h.tableStyles)||void 0===C?void 0:C.firstCol)||void 0===I?void 0:I.tcTxStyle)):h.props.lastCol&&h._isLastCol(e.td,n)?(dt=t(t(t({},dt),null===(T=null===(B=h.tableStyles)||void 0===B?void 0:B.lastCol)||void 0===T?void 0:T.tcStyle),{border:t(t({},null==dt?void 0:dt.border),null===(O=null===(_=null===(R=h.tableStyles)||void 0===R?void 0:R.lastCol)||void 0===_?void 0:_.tcStyle)||void 0===O?void 0:O.border)}),ut=t(t({},ut),null===(N=null===(E=h.tableStyles)||void 0===E?void 0:E.lastCol)||void 0===N?void 0:N.tcTxStyle)):(h.props.bandRow&&(h._isBandRow(a)?(dt=t(t(t({},dt),null===(F=null===(D=h.tableStyles)||void 0===D?void 0:D.band1H)||void 0===F?void 0:F.tcStyle),{border:t(t({},null==dt?void 0:dt.border),null===(z=null===(G=null===(Z=h.tableStyles)||void 0===Z?void 0:Z.band1H)||void 0===G?void 0:G.tcStyle)||void 0===z?void 0:z.border)}),ut=t(t({},ut),null===(H=null===(W=h.tableStyles)||void 0===W?void 0:W.band1H)||void 0===H?void 0:H.tcTxStyle)):(dt=t(t(t({},dt),null===(V=null===(Q=h.tableStyles)||void 0===Q?void 0:Q.band2V)||void 0===V?void 0:V.tcStyle),{border:t(t({},null==dt?void 0:dt.border),null===(U=null===(q=null===(X=h.tableStyles)||void 0===X?void 0:X.band2V)||void 0===q?void 0:q.tcStyle)||void 0===U?void 0:U.border)}),ut=t(t({},ut),null===(J=null===(Y=h.tableStyles)||void 0===Y?void 0:Y.band2V)||void 0===J?void 0:J.tcTxStyle))),h.props.bandCol&&(h._isBandCol(n)?(dt=t(t(t({},dt),null===($=null===(K=h.tableStyles)||void 0===K?void 0:K.band1V)||void 0===$?void 0:$.tcStyle),{border:t(t({},null==dt?void 0:dt.border),null===(at=null===(et=null===(tt=h.tableStyles)||void 0===tt?void 0:tt.band1V)||void 0===et?void 0:et.tcStyle)||void 0===at?void 0:at.border)}),ut=t(t({},ut),null===(nt=null===(rt=h.tableStyles)||void 0===rt?void 0:rt.band1V)||void 0===nt?void 0:nt.tcTxStyle)):(dt=t(t(t({},dt),null===(ct=null===(ot=h.tableStyles)||void 0===ot?void 0:ot.band2H)||void 0===ct?void 0:ct.tcStyle),{border:t(t({},null==dt?void 0:dt.border),null===(ht=null===(st=null===(it=h.tableStyles)||void 0===it?void 0:it.band2H)||void 0===st?void 0:st.tcStyle)||void 0===ht?void 0:ht.border)}),ut=t(t({},ut),null===(pt=null===(lt=h.tableStyles)||void 0===lt?void 0:lt.band2H)||void 0===pt?void 0:pt.tcTxStyle)))),r.inheritTcStyle=dt,r.inheritTcTxStyle=ut}))}))},r}(C),O=function(){function t(t,e,a,r){if(this.offset={x:0,y:0},this.chOffset={x:0,y:0},this.extend={w:0,h:0},this.chExtend={w:0,h:0},this.rotate=0,this.nodes=[],this.flipV=!1,this.flipH=!1,this.userDrawn=!0,this.order=c(t,["attrs","order"]),this.pptx=e,this.ctx=a,this.source=t,this.group=r,this.source["p:grpSpPr"]){var n=c(this.source,["p:grpSpPr","a:xfrm"]);if(n&&(this.offset={x:Math.round(this.group&&this.pptx.wps?y(parseInt(n["a:off"].attrs.x)):f(parseInt(n["a:off"].attrs.x))),y:Math.round(this.group&&this.pptx.wps?y(parseInt(n["a:off"].attrs.y)):f(parseInt(n["a:off"].attrs.y)))},this.chOffset={x:Math.round("point"===u(n["a:chOff"].attrs.x)||this.pptx.wps?y(parseInt(n["a:chOff"].attrs.x)):f(parseInt(n["a:chOff"].attrs.x))),y:Math.round("point"===u(n["a:chOff"].attrs.y)||this.pptx.wps?y(parseInt(n["a:chOff"].attrs.y)):f(parseInt(n["a:chOff"].attrs.y)))},this.chExtend={w:Math.round("point"===u(n["a:chExt"].attrs.cx)||this.pptx.wps?y(parseInt(n["a:chExt"].attrs.cx)):f(parseInt(n["a:chExt"].attrs.cx))),h:Math.round("point"===u(n["a:chExt"].attrs.cy)||this.pptx.wps?y(parseInt(n["a:chExt"].attrs.cy)):f(parseInt(n["a:chExt"].attrs.cy)))},this.extend={w:Math.round(this.group&&this.pptx.wps?y(parseInt(n["a:ext"].attrs.cx)):f(parseInt(n["a:ext"].attrs.cx))),h:Math.round(this.group&&this.pptx.wps?y(parseInt(n["a:ext"].attrs.cy)):f(parseInt(n["a:ext"].attrs.cy)))},this.rotate=w(parseInt(c(n,"attrs.rot",0))),this.flipV="1"===c(n,"attrs.flipV"),this.flipH="1"===c(n,"attrs.flipH")),r){var o=r.extend,i=r.chExtend,s=r.chOffset,h=o.w/i.w,l=o.h/i.h;this.extend.w=this.extend.w*h,this.extend.h=this.extend.h*l,this.offset.x=(this.offset.x-s.x)*h,this.offset.y=(this.offset.y-s.y)*l}}this._parseBackground(),this._parseNodes()}return t.prototype.getBackground=function(){return this.background&&"none"!==this.background.type?this.background:this.group?this.group.getBackground():void 0},t.prototype._parseBackground=function(){var t=c(this.source,["p:grpSpPr"]);t&&t["a:solidFill"]?this.background=g(t["a:solidFill"],this.ctx.theme,this.ctx):t&&t["a:gradFill"]?this.background=L(t["a:gradFill"],this.ctx.theme,this.ctx):t&&t["a:blipFill"]&&(this.background=x(t["a:blipFill"],this.pptx,this.ctx))},t.prototype._parseNodes=function(){var t=this.source;Z(this.nodes,t,this.pptx,this.ctx,this)},t}(),E=function(t){function n(e,a,r,n){var o=t.call(this,e,r,n)||this;return o.nodes=[],o.pptx=a,o}return e(n,t),n.prototype.parseNode=function(){return a(this,void 0,void 0,(function(){var t,e,a,n,o,i,s,h,l;return r(this,(function(r){switch(r.label){case 0:return r.trys.push([0,3,,4]),(t=c(this.source,["a:graphic","a:graphicData","dgm:relIds","attrs","r:dm"]))&&this.ctx.rels[t]?(e=this.ctx.rels[t].target,n=p,[4,this.pptx.getXmlByPath(e)]):[2];case 1:return a=n.apply(void 0,[r.sent()]),(o=c(a,["dgm:dataModel","dgm:extLst","a:ext","dsp:dataModelExt","attrs","relId"]))&&this.ctx.rels[o]?(i=this.ctx.rels[o].target,[4,this.pptx.getXmlByPath(i)]):[2];case 2:return s=(s=r.sent()).replace(/dsp:/g,"p:"),h=p(s),l=c(h,["p:drawing","p:spTree"]),Z(this.nodes,l,this.pptx,this.ctx),[3,4];case 3:return r.sent(),[3,4];case 4:return[2]}}))}))},n}(C),N=function(t){function o(e,a,r,n){var o=t.call(this,e,r,n)||this;return o.options={title:{},tooltip:{},legend:{},series:[],color:[]},o.userDrawn=!0,o.pptx=a,o}return e(o,t),Object.defineProperty(o.prototype,"slideMaster",{get:function(){return this.ctx.slideMaster||this.ctx},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"theme",{get:function(){return this.slideMaster.theme},enumerable:!1,configurable:!0}),o.prototype.parseNode=function(){return a(this,void 0,void 0,(function(){var t,e,a,n,o,i;return r(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),(t=c(this.source,["a:graphic","a:graphicData","c:chart","attrs","r:id"]))&&this.ctx.rels[t]?(e=this.ctx.rels[t].target,n=p,[4,this.pptx.getXmlByPath(e)]):[2];case 1:return a=n.apply(void 0,[r.sent()]),o=c(a,["c:chartSpace","c:chart"]),i=c(o,["c:plotArea"]),c(i,["c:lineChart"])?this.parseLine(c(i,["c:lineChart"]),o):c(i,["c:line3DChart"])?this.parseLine(c(i,["c:line3DChart"]),o):c(i,["c:areaChart"])?this.parseAreaLine(c(i,["c:areaChart"]),o):c(i,["c:area3DChart"])?this.parseAreaLine(c(i,["c:area3DChart"]),o):c(i,["c:barChart"])?this.parseBar(c(i,["c:barChart"]),o):c(i,["c:bar3DChart"])?this.parseBar(c(i,["c:bar3DChart"]),o):c(i,["c:pieChart"])?this.parsePie(c(i,["c:pieChart"])):c(i,["c:pie3DChart"])?this.parsePie(c(i,["c:pie3DChart"])):c(i,["c:doughnutChart"])&&this.parseDoughnutChart(c(i,["c:doughnutChart"])),[3,3];case 2:return r.sent(),[3,3];case 3:return[2]}}))}))},o.prototype.parseAreaLine=function(t,e){this.parseLine(t,e),this.options.series=this.options.series.map((function(t){return t.areaStyle={},t}))},o.prototype.parseLine=function(t,e){var a=c(t,["c:ser"]);Array.isArray(a)||(a=[a]),this.options.title={top:"top",left:"center",text:this.parseChartTitle(c(e,["c:title"]))},this.options.xAxis={type:"category",data:this.getCategory(a[0])},this.options.yAxis={type:"value"},this.options.series=this.parseLineSeries(a,t),this.options.color=this.parseLineColors(a),this.options.legend={bottom:"bottom",left:"center"},"percentStacked"===c(t,["c:grouping","attrs","val"])&&(this.options.tooltip.valueFormatter=function(t){return(100*t).toFixed(2)+"%"})},o.prototype.parseBar=function(t,e){var a=c(t,["c:ser"]);Array.isArray(a)||(a=[a]),this.options.title={top:"top",left:"center",text:this.parseChartTitle(c(e,["c:title"]))},"bar"===c(t,["c:barDir","attrs","val"])?(this.options.yAxis={type:"category",data:this.getCategory(a[0])},this.options.xAxis={type:"value"}):(this.options.xAxis={type:"category",data:this.getCategory(a[0])},this.options.yAxis={type:"value"}),this.options.series=this.parseBarSeries(a,t),this.options.color=this.parseBarColors(a),this.options.legend={bottom:"bottom",left:"center"},"percentStacked"===c(t,["c:grouping","attrs","val"])&&(this.options.tooltip.valueFormatter=function(t){return(100*t).toFixed(2)+"%"})},o.prototype.parsePie=function(t){var e=c(t,["c:ser"]);this.options.title={top:"top",left:"center",text:this.parsePieTitle(e)},this.options.color=this.parsePieColors(e),this.options.series=[this.parsePieSeries(e,t)],this.options.legend={bottom:"bottom",left:"center"}},o.prototype.parseDoughnutChart=function(t){var e=c(t,["c:ser"]);this.options.title.text=this.parsePieTitle(e),this.options.color=this.parsePieColors(e),this.options.series=[this.parsePieSeries(e,t)],this.options.legend={bottom:"bottom",left:"center"}},o.prototype.parsePieTitle=function(t){return c(t,["c:tx","c:strRef","c:strCache","c:pt","c:v"])},o.prototype.parseChartTitle=function(t){var e=c(t,["c:tx","c:rich","a:p"]);return Array.isArray(e)||(e=[e]),e.map((function(t){var e=c(t,["a:r"]);return Array.isArray(e)||(e=[e]),e.map((function(t){return c(t,["a:t"])||""})).join("")})).join("")||"图表标题"},o.prototype.parseBarColors=function(t){var e=this;return t.map((function(t){return S(g(c(t,["c:spPr","a:solidFill"]),e.theme,e.ctx))}))},o.prototype.parseLineColors=function(t){var e=this;return t.map((function(t){return S(g(c(t,["c:spPr","a:ln","a:solidFill"])||c(t,["c:spPr","a:solidFill"]),e.theme,e.ctx))}))},o.prototype.parsePieColors=function(t){var e=this,a=[],r=c(t,["c:dPt"]);return Array.isArray(r)||(r=[r]),r.forEach((function(t){a.push(S(g(c(t,["c:spPr","a:solidFill"]),e.theme,e.ctx)))})),a},o.prototype.parsePieSeries=function(t,e){var a={type:"pie",radius:"80%",startAngle:90,data:[]};c(e,["c:holeSize","attrs","val"])&&(a.radius=["".concat(.8*c(e,["c:holeSize","attrs","val"]),"%"),"80%"]);var r=c(e,["c:firstSliceAng","attrs","val"]);r&&(a.startAngle=90-r);for(var n=this.getCategory(t),o=this.getVal(t),i=0;i<n.length;i++)a.data.push({name:n[i],value:o[i]});return a},o.prototype.parseBarSeries=function(t,e){var a,r=this,o=c(e,["c:grouping","attrs","val"]);"stacked"===o?a="Ad":"percentStacked"===o&&(a="total");var i=t.map((function(t){return{type:"bar",name:c(t,["c:tx","c:strRef","c:strCache","c:pt","c:v"]),data:r.getVal(t),stack:a}}));if("percentStacked"===o){var s=[];i.forEach((function(t,e){s=0===e?n([],t.data,!0):s.map((function(e,a){return e+t.data[a]}))})),i.forEach((function(t){t.data=t.data.map((function(t,e){return s[e]<=0?0:t/s[e]}))}))}return i},o.prototype.parseLineSeries=function(t,e){var a,r=this,o=c(e,["c:grouping","attrs","val"]);"stacked"===o?a="Ad":"percentStacked"===o&&(a="total");var i=t.map((function(t){return{type:"line",name:c(t,["c:tx","c:strRef","c:strCache","c:pt","c:v"]),data:r.getVal(t),stack:a}}));if("total"===a){var s=[];i.forEach((function(t,e){s=0===e?n([],t.data,!0):s.map((function(e,a){return e+t.data[a]}))})),i.forEach((function(t){t.data=t.data.map((function(t,e){return s[e]<=0?0:t/s[e]}))}))}return i},o.prototype.getCategory=function(t){if(c(t,["c:cat","c:strRef"])){var e=c(t,["c:cat","c:strRef","c:strCache","c:pt"]);return Array.isArray(e)||(e=[e]),e.map((function(t){return c(t,["c:v"])}))}if(c(t,["c:cat","c:numRef"])){e=c(t,["c:cat","c:numRef","c:numCache","c:pt"]);return Array.isArray(e)||(e=[e]),e.map((function(t){return c(t,["c:v"])}))}},o.prototype.getVal=function(t){var e=c(t,["c:val","c:numRef","c:numCache","c:pt"]);return Array.isArray(e)||(e=[e]),e.map((function(t){return+c(t,["c:v"])}))},o}(C);function D(t){var e={},a=c(t,"attrs")||{};return Object.keys(a).forEach((function(t){switch(t){case"algn":e.align=a[t];break;case"marL":e.marginLeft=f(parseInt(a[t]));break;case"indent":e.indent=f(parseInt(a[t]));break;case"lvl":e.level=a[t]}})),c(t,["a:lnSpc","a:spcPct","attrs","val"])&&(e.lineHeight=parseInt(t["a:lnSpc"]["a:spcPct"].attrs.val)/1e5),e}function F(t,e,a){var r={},n=c(t,"attrs")||{};Object.keys(n).forEach((function(t){switch(t){case"sz":r.size=parseInt(n[t])/100;break;case"b":r.bold="1"===n[t];break;case"i":r.italic="1"===n[t];break;case"u":r.underline=n[t];break;case"strike":r.strike=n[t];break;case"order":case"dirty":break;default:r[t]=n[t]}}));var o=c(t,"a:solidFill");return o&&(r.color=g(o,e,a)),r}function Z(t,e,n,o,i){return a(this,void 0,void 0,(function(){var a,s,h,l,p,d,u,f,y,v,w,b,m,g,x,L,M,A,P,k,j;return r(this,(function(r){switch(r.label){case 0:for(h in s=[],a=e)s.push(h);l=0,r.label=1;case 1:if(!(l<s.length))return[3,17];if(!((h=s[l])in a))return[3,16];switch(p=h){case"p:sp":return[3,2];case"p:pic":return[3,3];case"p:cxnSp":return[3,4];case"p:graphicFrame":return[3,5];case"p:grpSp":return[3,14]}return[3,15];case 2:for(d=Array.isArray(e[p])?e[p]:[e[p]],k=0;k<d.length;k++)m=d[k],t.push(new T(m,n,o,i));return[3,16];case 3:for(u=Array.isArray(e[p])?e[p]:[e[p]],k=0;k<u.length;k++)f=u[k],y=f["p:blipFill"]["a:blip"].attrs["r:embed"],v=o.rels[y].target,w=new R(v,f,n,o,i),t.push(w);return[3,16];case 4:for(b=Array.isArray(e[p])?e[p]:[e[p]],k=0;k<b.length;k++)m=b[k],t.push(new T(m,n,o,i));return[3,16];case 5:g=Array.isArray(e[p])?e[p]:[e[p]],k=0,r.label=6;case 6:if(!(k<g.length))return[3,13];switch(x=g[k],L=c(x,["a:graphic","a:graphicData","attrs","uri"]),L){case"http://schemas.openxmlformats.org/drawingml/2006/table":return[3,7];case"http://schemas.openxmlformats.org/drawingml/2006/diagram":return[3,8];case"http://schemas.openxmlformats.org/drawingml/2006/chart":return[3,10]}return[3,12];case 7:return t.push(new _(x,n,o,i)),[3,12];case 8:return[4,(M=new E(x,n,o,i)).parseNode()];case 9:return r.sent(),t.push(M),[3,12];case 10:return[4,(A=new N(x,n,o,i)).parseNode()];case 11:return r.sent(),t.push(A),[3,12];case 12:return k++,[3,6];case 13:return[3,16];case 14:for(P=Array.isArray(e[p])?e[p]:[e[p]],k=0;k<P.length;k++)j=P[k],t.push(new O(j,n,o,i));return[3,16];case 15:return[3,16];case 16:return l++,[3,1];case 17:return[2]}}))}))}var G=function(){function t(t,e,a){this.slideType="slide",this.rels={},this.background={type:"none"},this.nodes=[],this.name=t,this.source=e,this.pptx=a}return Object.defineProperty(t.prototype,"index",{get:function(){if(!this.name)return 0;var t=this.name.match(/(\d+)/);return t?parseInt(t[0]):1},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"slideMaster",{get:function(){return this.slideLayout&&this.slideLayout.slideMaster},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"theme",{get:function(){return this.slideMaster.theme},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"_relsPath",{get:function(){return this.name.replace("slides/slide","slides/_rels/slide")+".rels"},enumerable:!1,configurable:!0}),t.prototype.load=function(){return a(this,void 0,void 0,(function(){return r(this,(function(t){switch(t.label){case 0:return[4,this._loadRels()];case 1:return t.sent(),this._loadBackground(),[4,this._loadNodes()];case 2:return t.sent(),[2]}}))}))},t.prototype._loadRels=function(){return a(this,void 0,void 0,(function(){var t,e,a,n=this;return r(this,(function(r){switch(r.label){case 0:return e=p,[4,this.pptx.getXmlByPath(this._relsPath)];case 1:return t=e.apply(void 0,[r.sent()]),a=c(t,["Relationships","Relationship"])||[],Array.isArray(a)||(a=[a]),a.forEach((function(t){switch(c(t,["attrs","Type"])){case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideLayout":var e=t.attrs.Target.replace("../","ppt/");e.startsWith("/ppt")&&(e=e.substr(1)),n.slideLayout=n.pptx.getSlideLayout(e);break;case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image":case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/audio":case"http://schemas.microsoft.com/office/2007/relationships/media":case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/video":case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/diagramLayout":case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/diagramData":case"http://schemas.microsoft.com/office/2007/relationships/diagramDrawing":case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/diagramColors":case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart":var a=t.attrs.Target.replace("../","ppt/");a.startsWith("/ppt")&&(a=a.substr(1)),n.rels[t.attrs.Id]={type:t.attrs.Type.split("/").pop(),target:a}}})),[2]}}))}))},t.prototype._loadBackground=function(){var t=c(this.source,["p:sld","p:cSld","p:bg","p:bgPr"]);t&&t["a:solidFill"]?this.background=g(t["a:solidFill"],this.theme,this):t&&t["a:gradFill"]?this.background=L(t["a:gradFill"],this.theme,this):t&&t["a:blipFill"]&&(this.background=x(t["a:blipFill"],this.pptx,this))},t.prototype._loadNodes=function(){return a(this,void 0,void 0,(function(){var t;return r(this,(function(e){switch(e.label){case 0:return t=c(this.source,["p:sld","p:cSld","p:spTree"]),[4,Z(this.nodes,t,this.pptx,this)];case 1:return e.sent(),[2]}}))}))},t.prototype.getColorThemeName=function(t){return this.slideLayout.getColorThemeName(t)},t.prototype.getNodeInheritAttrsByType=function(t,e){var a=this.slideLayout.getNodeByType(t),r=c(a.source,e);return r||this.slideLayout.getNodeInheritAttrsByType(t,e)},t.prototype.getNodeInheritAttrsByIdx=function(t,e){var a=this.slideLayout.getNodeByIdx(t),r=c(a.source,e);return r||this.slideLayout.getNodeInheritAttrsByIdx(t,e)},t}(),z=function(){function t(t,e,a){this.slideType="slideLayout",this.rels={},this.background={type:"none"},this.nodes=[],this.name=t,this.source=e,this.pptx=a}return Object.defineProperty(t.prototype,"_relsPath",{get:function(){return this.name.replace("slideLayouts/slideLayout","slideLayouts/_rels/slideLayout")+".rels"},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"theme",{get:function(){return this.slideMaster.theme},enumerable:!1,configurable:!0}),t.prototype.load=function(){return a(this,void 0,void 0,(function(){return r(this,(function(t){switch(t.label){case 0:return[4,this._loadRels()];case 1:return t.sent(),[4,this._loadBackground()];case 2:return t.sent(),[4,this._loadNodes()];case 3:return t.sent(),[2]}}))}))},t.prototype._loadRels=function(){return a(this,void 0,void 0,(function(){var t,e,a,n=this;return r(this,(function(r){switch(r.label){case 0:return e=p,[4,this.pptx.getXmlByPath(this._relsPath)];case 1:return t=e.apply(void 0,[r.sent()]),a=c(t,["Relationships","Relationship"])||[],Array.isArray(a)||(a=[a]),a.forEach((function(t){switch(c(t,["attrs","Type"])){case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/slideMaster":var e=t.attrs.Target.replace("../","ppt/");e.startsWith("/ppt")&&(e=e.substr(1)),n.slideMaster=n.pptx.getSlideMaster(e);break;case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image":case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/audio":case"http://schemas.microsoft.com/office/2007/relationships/media":case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/video":var a=t.attrs.Target.replace("../","ppt/");a.startsWith("/ppt")&&(a=a.substr(1)),n.rels[t.attrs.Id]={type:t.attrs.Type.split("/").pop(),target:a}}})),[2]}}))}))},t.prototype._loadBackground=function(){return a(this,void 0,void 0,(function(){var t;return r(this,(function(e){return(t=c(this.source,["p:sldLayout","p:cSld","p:bg","p:bgPr"]))&&t["a:solidFill"]?this.background=g(t["a:solidFill"],this.theme):t&&t["a:gradFill"]?this.background=L(t["a:gradFill"],this.theme,this):t&&t["a:blipFill"]&&(this.background=x(t["a:blipFill"],this.pptx,this)),[2]}))}))},t.prototype._loadNodes=function(){return a(this,void 0,void 0,(function(){var t;return r(this,(function(e){return t=c(this.source,["p:sldLayout","p:cSld","p:spTree"]),Z(this.nodes,t,this.pptx,this),[2]}))}))},t.prototype.getColorThemeName=function(t){return this.slideMaster.getColorThemeName(t)},t.prototype.getNodeByType=function(t){return this.nodes.find((function(e){return e.type===t}))},t.prototype.getNodeByIdx=function(t){return this.nodes.find((function(e){return e.idx===t}))},t.prototype.getNodeInheritAttrsByType=function(t,e){var a=this.slideMaster.getNodeByType(t);return a&&c(a.source,e)},t.prototype.getNodeInheritAttrsByIdx=function(t,e){var a=this.slideMaster.getNodeByIdx(t);return a&&c(a.source,e)},t}(),W=function(){function t(t,e,a){this.slideType="slideMaster",this.rels={},this.background={type:"none"},this.textStyles={titleStyle:{},bodyStyle:{},otherStyle:{}},this.defaultTextStyle={},this.nodes=[],this.tableStyles={},this.name=t,this.source=e,this.pptx=a,this.load()}return Object.defineProperty(t.prototype,"_relsPath",{get:function(){return this.name.replace("slideMasters/slideMaster","slideMasters/_rels/slideMaster")+".rels"},enumerable:!1,configurable:!0}),t.prototype.load=function(){return a(this,void 0,void 0,(function(){return r(this,(function(t){switch(t.label){case 0:return[4,this._parseRels()];case 1:return t.sent(),this._parseColorMap(),this._parseBackground(),this._parseTextStyles(),this._parseTableStyles(),this._parseDefaultTextStyle(),this._loadNodes(),[2]}}))}))},t.prototype._parseRels=function(){return a(this,void 0,void 0,(function(){var t,e,a,n=this;return r(this,(function(r){switch(r.label){case 0:return e=p,[4,this.pptx.getXmlByPath(this._relsPath)];case 1:return t=e.apply(void 0,[r.sent()]),a=c(t,["Relationships","Relationship"])||[],Array.isArray(a)||(a=[a]),a.forEach((function(t){switch(c(t,["attrs","Type"])){case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme":var e=t.attrs.Target.replace("../","ppt/");e.startsWith("/ppt")&&(e=e.substr(1)),n.theme=n.pptx.getTheme(e);break;case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image":case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/audio":case"http://schemas.microsoft.com/office/2007/relationships/media":case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/video":var a=t.attrs.Target.replace("../","ppt/");a.startsWith("/ppt")&&(a=a.substr(1)),n.rels[t.attrs.Id]={type:t.attrs.Type.split("/").pop(),target:a}}})),[2]}}))}))},t.prototype._parseColorMap=function(){this.colorMap=i(c(this.source,["p:sldMaster","p:clrMap","attrs"])||{},["order"])},t.prototype.getColorThemeName=function(t){return this.colorMap[t]||t},t.prototype._parseBackground=function(){var t=c(this.source,["p:sldMaster","p:cSld","p:bg","p:bgPr"]),e=c(this.source,["p:sldMaster","p:cSld","p:bg","p:bgRef"]);t&&t["a:solidFill"]?this.background=g(t["a:solidFill"],this.theme,this):t&&t["a:gradFill"]?this.background=L(t["a:gradFill"],this.theme,this):t&&t["a:blipFill"]?this.background=x(t["a:blipFill"],this.pptx,this):e&&(this.background=g(e,this.theme,this))},t.prototype._parseDefaultTextStyle=function(){var t=this,e=this.pptx.defaultTextStyleSource;Object.keys(e).forEach((function(a){if(a.startsWith("a:")&&a.endsWith("pPr")){var r=a.substr(2,a.length-5),n=c(e[a],["a:defRPr"]);t.defaultTextStyle[r]={props:D(e[a]),defRPr:F(n,t.theme,t)}}}))},t.prototype._parseTextStyles=function(){var t=this,e=c(this.source,["p:sldMaster","p:txStyles"]);["titleStyle","bodyStyle","otherStyle"].forEach((function(a){var r=t.textStyles[a],n=c(e,"p:".concat(a))||{};Object.keys(n).forEach((function(e){if(e.startsWith("a:")&&e.endsWith("pPr")){var a=e.substr(2,e.length-5);r[a]={},r[a].props=D(n[e]);var o=c(n[e],["a:defRPr"]);r[a].defRPr=F(o,t.theme,t)}}))}))},t.prototype._parseTableStyles=function(){var t=this,e={},a=c(this.pptx.tableStyles,["a:tblStyleLst","a:tblStyle"])||[];Array.isArray(a)||(a=[a]),a.forEach((function(a){var r=c(a,["attrs","styleId"]);e[r]={},Object.keys(a).forEach((function(n){if(n.startsWith("a:")){var o=n.substr(2);e[r][o]={};var i=c(a[n],["a:tcStyle"]);if(i){var s={};c(i,["a:fill","a:solidFill"])&&(s.background=g(c(i,["a:fill","a:solidFill"]),t.theme,t));var h=c(i,"a:tcBdr");h&&(s.border={},Object.keys(h).forEach((function(e){if(e.startsWith("a:")){var a=e.substr(2),r=c(h[e],["a:ln"]);s.border[a]=B(r,t.theme,t)}}))),e[r][o].tcStyle=s}var l=c(a[n],["a:tcTxStyle"]);if(l){var p={};p.color=g(l,t.theme,t),"on"===c(l,["attrs","b"])&&(p.bold=!0),e[r][o].tcTxStyle=p}}}))})),this.tableStyles=e},t.prototype._loadNodes=function(){return a(this,void 0,void 0,(function(){var t;return r(this,(function(e){return t=c(this.source,["p:sldMaster","p:cSld","p:spTree"]),Z(this.nodes,t,this.pptx,this),[2]}))}))},t.prototype.getNodeByType=function(t){return this.nodes.find((function(e){return e.type===t}))},t.prototype.getNodeByIdx=function(t){return this.nodes.find((function(e){return e.idx===t}))},t.prototype.getNodeInheritAttrsByType=function(t,e){},t.prototype.getNodeInheritAttrsByIdx=function(t,e){},t}(),H=function(){function t(t,e,a){this.clrScheme={},this.borderScheme=[],this.name=t,this.source=e,this.pptx=a,this._parseClrScheme(),this._parseLineStyleLst()}return t.prototype._parseClrScheme=function(){var t=c(this.source,["a:theme","a:themeElements","a:clrScheme"]);for(var e in t)if(e.startsWith("a:")){var a=e.substring(2),r=c(t[e],["a:sysClr","attrs","lastClr"])||c(t[e],["a:srgbClr","attrs","val"]);this.clrScheme[a]="#"+r}},t.prototype._parseLineStyleLst=function(){var t=this,e=c(this.source,["a:theme","a:themeElements","a:fmtScheme","a:lnStyleLst","a:ln"])||[];this.borderScheme=e.map((function(e){var a={color:{}};return c(e,["attrs","w"])&&(a.width=f(parseInt(c(e,["attrs","w"])))),c(e,["attrs","algn"])&&(a.algn=c(e,["attrs","algn"])),c(e,["attrs","cap"])&&(a.cap=c(e,["attrs","cap"])),c(e,["attrs","cmpd"])&&(a.cmpd=c(e,["attrs","cmpd"])),c(e,["a:miter","attrs","lim"])&&(a.miterLim=f(parseInt(c(e,["a:miter","attrs","lim"])))),c(e,["a:prstDash","attrs","val"])&&(a.type=c(e,["a:prstDash","attrs","val"])),c(e,["a:solidFill"])&&(a.color=g(c(e,["a:solidFill"]),t)),a}))},t.prototype.getColor=function(t){if("phClr"!==t)return this.clrScheme[t]||this.defaultColor},t.prototype.getLineStyle=function(t){return this.borderScheme[t-1]},t}();var Q=function(){function t(){this.slides=[],this.slideLayouts=[],this.slideMaster=[],this.themes=[],this.medias={},this.wps=!1}return t.prototype.load=function(t){return a(this,void 0,void 0,(function(){var e,a;return r(this,(function(r){switch(r.label){case 0:return e=new o,a=this,[4,e.loadAsync(t)];case 1:return a._zipContents=r.sent(),[4,this._loadThumbnail()];case 2:return r.sent(),[4,this._loadMedia()];case 3:return r.sent(),[4,this._loadPresentation()];case 4:return r.sent(),[4,this._loadContentTypes()];case 5:return r.sent(),[2]}}))}))},t.prototype._loadThumbnail=function(){return a(this,void 0,void 0,(function(){var t;return r(this,(function(e){switch(e.label){case 0:return e.trys.push([0,3,,4]),this._zipContents.files["docProps/thumbnail.jpeg"]?[4,this._zipContents.files["docProps/thumbnail.jpeg"].async("base64")]:[3,2];case 1:t=e.sent(),this.thumbnail="data:image/jpeg;base64,"+t,e.label=2;case 2:return[3,4];case 3:return e.sent(),[3,4];case 4:return[2]}}))}))},t.prototype._loadPresentation=function(){return a(this,void 0,void 0,(function(){var t,e,a,n,o,i;return r(this,(function(r){switch(r.label){case 0:return r.trys.push([0,4,,5]),[4,this._zipContents.files["ppt/presentation.xml"].async("text")];case 1:return t=r.sent(),e=p(t),a=e["p:presentation"]["p:sldSz"].attrs,this.width=f(parseInt(a.cx)),this.height=f(parseInt(a.cy)),this.defaultTextStyleSource=c(e,["p:presentation","p:defaultTextStyle"]),this._zipContents.files["docProps/app.xml"]?[4,this._zipContents.files["docProps/app.xml"].async("text")]:[3,3];case 2:n=r.sent(),o=p(n),i=c(o,["Properties","Application"]),this.wps=(null==i?void 0:i.includes("WPS"))||!1,r.label=3;case 3:return[3,5];case 4:return r.sent(),[3,5];case 5:return[2]}}))}))},t.prototype._loadContentTypes=function(){return a(this,void 0,void 0,(function(){var t,e,a,n,o,c,i,s,h,l,d,u,f,y,v,w,b,m,g,x,L;return r(this,(function(r){switch(r.label){case 0:return r.trys.push([0,22,,23]),e=p,[4,this._zipContents.files["[Content_Types].xml"].async("text")];case 1:if(t=e.apply(void 0,[r.sent()]),a=t.Types.Override,!(n=a.filter((function(t){return"application/vnd.openxmlformats-officedocument.theme+xml"===t.attrs.ContentType}))))return[3,5];o=0,c=n,r.label=2;case 2:return o<c.length?(i=c[o],m=i.attrs.PartName.substr(1),s=p,[4,this._zipContents.files[m].async("text")]):[3,5];case 3:g=s.apply(void 0,[r.sent()]),this.themes.push(new H(m,g,this)),r.label=4;case 4:return o++,[3,2];case 5:return(h=a.find((function(t){return"application/vnd.openxmlformats-officedocument.presentationml.tableStyles+xml"===t.attrs.ContentType})))?(m=h.attrs.PartName.substr(1),l=p,[4,this._zipContents.files[m].async("text")]):[3,7];case 6:g=l.apply(void 0,[r.sent()]),this.tableStyles=g,r.label=7;case 7:d=a.filter((function(t){return"application/vnd.openxmlformats-officedocument.presentationml.slideMaster+xml"===t.attrs.ContentType})),b=0,r.label=8;case 8:return b<d.length?(m=d[b].attrs.PartName.substr(1),u=p,[4,this._zipContents.files[m].async("text")]):[3,11];case 9:g=u.apply(void 0,[r.sent()]),this.slideMaster.push(new W(m,g,this)),r.label=10;case 10:return b++,[3,8];case 11:f=a.filter((function(t){return"application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml"===t.attrs.ContentType})),b=0,r.label=12;case 12:return b<f.length?(m=f[b].attrs.PartName.substr(1),y=p,[4,this._zipContents.files[m].async("text")]):[3,16];case 13:return g=y.apply(void 0,[r.sent()]),[4,(v=new z(m,g,this)).load()];case 14:r.sent(),this.slideLayouts.push(v),r.label=15;case 15:return b++,[3,12];case 16:w=a.filter((function(t){return"application/vnd.openxmlformats-officedocument.presentationml.slide+xml"===t.attrs.ContentType})),b=0,r.label=17;case 17:return b<w.length?(m=w[b].attrs.PartName.substr(1),x=p,[4,this._zipContents.files[m].async("text")]):[3,21];case 18:return g=x.apply(void 0,[r.sent()]),[4,(L=new G(m,g,this)).load()];case 19:r.sent(),this.slides.push(L),r.label=20;case 20:return b++,[3,17];case 21:return this.slides.sort((function(t,e){return t.index-e.index})),[3,23];case 22:return r.sent(),[3,23];case 23:return[2]}}))}))},t.prototype._loadMedia=function(){return a(this,void 0,void 0,(function(){var t,e,a,n,o,c,i,s,h,l,p,d,u,f=this;return r(this,(function(r){switch(r.label){case 0:t=Object.keys(this._zipContents.files).filter((function(t){return t.startsWith("ppt/media/image")})).map((function(t){return f._zipContents.files[t]})),e=0,a=t,r.label=1;case 1:return e<a.length?(n=a[e],o=function(t){var e;switch(t){case"jpg":case"jpeg":e="image/jpeg";break;case"png":e="image/png";break;case"gif":e="image/gif";break;case"emf":e="image/x-emf";break;case"wmf":e="image/x-wmf";break;default:e="image/*"}return e}((y=n.name).substr(2+(~-y.lastIndexOf(".")>>>0))),[4,n.async("base64")]):[3,4];case 2:c=r.sent(),this.medias[n.name]="data:".concat(o,";base64,").concat(c),r.label=3;case 3:return e++,[3,1];case 4:i=Object.keys(this._zipContents.files).filter((function(t){return t.startsWith("ppt/media/media")&&["mp3","wav","ogg","mp4","webm"].includes(t.split(".").pop().toLowerCase())})).map((function(t){return f._zipContents.files[t]})),s=0,h=i,r.label=5;case 5:return s<h.length?(l=h[s],p=l.name.split(".").pop().toLowerCase(),[4,l.async("arraybuffer")]):[3,8];case 6:d=r.sent(),u=new Blob([d],{type:"".concat(["mp3","wav"].includes(p)?"audio":"video","/").concat(p)}),this.medias[l.name]=URL.createObjectURL(u),r.label=7;case 7:return s++,[3,5];case 8:return[2]}var y}))}))},t.prototype.getXmlByPath=function(t){return a(this,void 0,void 0,(function(){return r(this,(function(e){switch(e.label){case 0:if(!this._zipContents.files[t])throw new Error("文件不存在");return[4,this._zipContents.files[t].async("text")];case 1:return[2,e.sent()]}}))}))},t.prototype.getSlideLayout=function(t){return this.slideLayouts.find((function(e){return e.name===t}))},t.prototype.getSlideMaster=function(t){return this.slideMaster.find((function(e){return e.name===t}))},t.prototype.getTheme=function(t){return this.themes.find((function(e){return e.name===t}))},t.prototype.getMedia=function(t){return this.medias[t]},t}();function V(t){var e=t.extend,a=t.offset,r=t.clip,n=t.base64,o=t.audioFile,c=t.videoFile,i=document.createElement("div");i.style.setProperty("position","absolute"),i.style.setProperty("left",a.x+"px"),i.style.setProperty("top",a.y+"px");var s,h,l,p,d=document.createElement("div");d.style.setProperty("position","absolute"),d.style.setProperty("left","0"),d.style.setProperty("top","0"),d.style.setProperty("width",e.w+"px"),d.style.setProperty("height",e.h+"px"),d.style.setProperty("overflow","hidden"),r?(s=e.w/(1-(void 0===r.l?0:r.l)-(void 0===r.r?0:r.r)),h=e.h/(1-(void 0===r.t?0:r.t)-(void 0===r.b?0:r.b)),l=-1*s*(void 0===r.l?0:r.l),p=-1*h*(void 0===r.t?0:r.t)):(s=e.w,h=e.h,l=0);var u=document.createElement("img");if(u.src=n,u.width=s,u.height=h,u.style.setProperty("position","absolute"),u.style.setProperty("left",l+"px"),u.style.setProperty("top",p+"px"),d.append(u),i.append(d),o){var f=document.createElement("audio");f.style.position="absolute",f.style.left="0",f.style.top="0",f.src=o,f.controls=!0,f.style.transform="translate(-50%)",i.append(f)}if(c){var y=document.createElement("video");y.style.position="absolute",y.style.left="0",y.style.top="0",y.width=e.w,y.height=e.h,y.src=c,y.controls=!0,i.append(y)}return i}function X(e,a,r){var n,o=e.props,c=e.text,i=t(t({},a),o),s=document.createElement("span");s.innerHTML="string"==typeof c?c:"";var h=18;i.size&&((null===(n=null==r?void 0:r.normAutofit)||void 0===n?void 0:n.fontScale)?(h=i.size*r.normAutofit.fontScale,s.style.fontSize=h+"px"):(h=i.size,s.style.fontSize=h+"px"));var l=S(i.color);l&&(s.style.color=l);var p=/^[^\u4e00-\u9fff]+$/;if(i.typeface)switch(s.style.fontFamily=i.typeface,i.typeface){case"DengXian":p.test(c)&&(s.style.letterSpacing=-.04*h+"px");break;case"DengXian Light":p.test(c)&&(s.style.letterSpacing=-.05*h+"px");break;case"STLiti":case"SimSun":case"NSimSun":case"SimHei":p.test(c)&&(s.style.fontSize=.85*parseInt(s.style.fontSize)+"px");break;case"华文中宋":case"Fira Sans Extra Condensed Medium":s.style.fontSize=.85*parseInt(s.style.fontSize)+"px";break;case"FangSong":s.style.letterSpacing=-.08*h+"px"}else p.test(c)&&(s.style.letterSpacing=-.04*h+"px");return i.bold&&(s.style.fontWeight="bold"),i.italic&&(s.style.fontStyle="italic"),i.underline&&"none"!==i.underline&&(s.style.textDecoration="underline"),i.background&&(s.style.backgroundColor=S(i.background)),s.style.wordBreak="break-word",s}function q(t,e,a){var r=document.createElement("span"),n=t.firstElementChild;switch(r.style.fontSize=n.style.fontSize,r.style.color=n.style.color,r.style.fontWeight=n.style.fontWeight,r.style.fontStyle=n.style.fontStyle,r.style.marginRight="10px",e.buAutoNum){case"arabicPeriod":default:r.textContent=a+".";break;case"circleNumDbPlain":r.textContent=["①","②","③","④","⑤","⑥","⑦","⑧","⑨","⑩","⑪","⑫","⑬","⑭","⑮","⑯","⑰","⑱","⑲","⑳"][a-1]||a+"";break;case"romanUcPeriod":r.textContent=function(t){var e=[{value:1e3,numeral:"M"},{value:900,numeral:"CM"},{value:500,numeral:"D"},{value:400,numeral:"CD"},{value:100,numeral:"C"},{value:90,numeral:"XC"},{value:50,numeral:"L"},{value:40,numeral:"XL"},{value:10,numeral:"X"},{value:9,numeral:"IX"},{value:5,numeral:"V"},{value:4,numeral:"IV"},{value:1,numeral:"I"}];if("number"!=typeof t||t<1||t>3999)throw new Error("Input must be a number between 1 and 3999.");for(var a="",r=0;r<e.length;r++)for(;t>=e[r].value;)a+=e[r].numeral,t-=e[r].value;return a}(a)+".";break;case"alphaUcPeriod":r.textContent=m(a)+".";break;case"alphaLcPeriod":r.textContent=m(a).toLowerCase()+".";break;case"alphaLcParenR":r.textContent=m(a).toLowerCase()+")";break;case"ea1JpnChsDbPeriod":r.textContent=function(t){var e=["〇","一","二","三","四","五","六","七","八","九"];if(!Number.isInteger(t)||t<0)return"";for(var a="",r=t.toString(),n=0;n<r.length;n++)a+=e[parseInt(r[n],10)];return a}(a)+"."}t.prepend(r)}function U(e,a,r){var n,o,c,i;void 0===a&&(a=0),void 0===r&&(r={});var s=e.inheritProps,h=e.inheritRProps,l=e.props,p=e.rows,d=t(t({},s),l),u=function(){for(var t,e,a=0,n=0,o=p;n<o.length;n++){var c=o[n];c.props&&c.props.size&&(a=Math.max(a,c.props.size))}var i=(null===(e=null===(t=null==r?void 0:r.bodyProps)||void 0===t?void 0:t.normAutofit)||void 0===e?void 0:e.fontScale)||1;return(a||h.size||18)*i},f=document.createElement("div"),y=r.isFirst?0:d.spaceBefore||0,v=r.isLast?0:d.spaceAfter||0;f.style.margin="".concat(Math.floor(.2*u()),"px  0 0 0"),f.style.padding="".concat(Math.floor(y),"px 0px ").concat(Math.floor(v),"px 0px");var w=document.createElement("p");w.style.margin="0",w.style.padding="0px",w.style.wordBreak="break-word";w.style.textAlign=d.align&&{ctr:"center",l:"left",r:"right",dist:"justify"}[d.align]||"center","dist"===d.align&&(w.style.textAlignLast="justify");var b=d.hasOwnProperty("lineHeight")?d.lineHeight:1;if((null===(o=null===(n=r.bodyProps)||void 0===n?void 0:n.normAutofit)||void 0===o?void 0:o.lnSpcReduction)&&(b*=1-(null===(i=null===(c=r.bodyProps)||void 0===c?void 0:c.normAutofit)||void 0===i?void 0:i.lnSpcReduction)),w.style.lineHeight=b+"",w.style.fontSize=u()+"px",p.length){for(var m=0,g=p;m<g.length;m++){var x=g[m];x.isBr?w.appendChild(document.createElement("br")):w.appendChild(X(x,t(t({},h),{marginTop:Math.floor(.2*u())}),r.bodyProps))}d.buAutoNum?q(w,d,a):d.buChar&&function(t,e){var a=document.createElement("span"),r=t.firstElementChild;a.style.color=r.style.color,a.style.fontSize=r.style.fontSize,a.textContent={n:"■",l:"●",u:"◆",p:"□","ü":"✔","Ø":"➢","•":"•"}[e.buChar]||"■",a.style.marginRight="10px",t.prepend(a)}(w,d),w.style.paddingLeft=(d.marginLeft||0)+(d.indent||0)+"px"}else{var L=document.createElement("span");L.innerHTML="&nbsp;",L.style.fontSize=h.size+"px",w.appendChild(L)}return f.appendChild(w),f}var Y="http://www.w3.org/2000/svg";function J(t){return document.createElementNS(Y,t)}function K(t){var e=t.extend;return.16667*Math.min(e.w,e.h)}function $(t,e,a){void 0===a&&(a=0);var r=e.prstGeom,n=((null==r?void 0:r.gd)||[]).find((function(e){return e.name===t}));return n?n.fmla:a}function tt(t,e,a){void 0===a&&(a=0);var r=e.extend,n=e.prstGeom,o=((null==n?void 0:n.gd)||[]).find((function(e){return e.name===t}));return o?Math.min(r.w,r.h)*o.fmla:a}function et(t,e,a){void 0===a&&(a=0);var r=e.extend,n=e.prstGeom,o=((null==n?void 0:n.gd)||[]).find((function(e){return e.name===t}));return o?Math.max(r.w,r.h)*o.fmla:a}function at(t,e){var a=0;switch(t){case"sm":a=1*e;break;case"med":a=1.5*e;break;case"lg":a=2.5*e}return Math.max(a,2)}function rt(t,e,a,r){void 0===r&&(r=!1);var n=t.border,o=void 0===n?{}:n,c=t.uuid,i=o.headEnd,s=o.width,h=o.color,l=o.tailEnd,p=r?i:l,d=p.len,u=void 0===d?"med":d,f=p.w,y=void 0===f?"med":f,v=at(u,s),w=at(y,s),b=J("defs"),m=J("marker"),g="marker-".concat(c,"-").concat(r?"start":"end");m.setAttribute("id",g),m.setAttribute("viewBox","0 0 ".concat(2*v," ").concat(2*w)),m.setAttribute("refX",v+"px"),m.setAttribute("refY",w+"px"),m.setAttribute("markerWidth",2*v+"px"),m.setAttribute("markerHeight",2*w+"px"),m.setAttribute("orient","auto"),m.setAttribute("markerUnits","userSpaceOnUse");var x=J("ellipse");x.setAttribute("cx",v+"px"),x.setAttribute("cy",w+"px"),x.setAttribute("rx",v+"px"),x.setAttribute("ry",w+"px"),x.setAttribute("fill",S(h)||"transparent"),m.appendChild(x),b.appendChild(m),e.appendChild(b),a.setAttribute(r?"marker-start":"marker-end","url(#".concat(g,")"))}function nt(t,e,a,r){void 0===r&&(r=!1);var n=t.border,o=void 0===n?{}:n,c=t.uuid,i=o.headEnd,s=o.width,h=o.color,l=o.tailEnd,p=r?i:l,d=p.len,u=void 0===d?"med":d,f=p.w,y=void 0===f?"med":f,v=at(u,s),w=at(y,s),b=J("defs"),m=J("marker"),g="marker-".concat(c,"-").concat(r?"start":"end");m.setAttribute("id",g),m.setAttribute("viewBox","0 0 ".concat(2*v," ").concat(2*w)),m.setAttribute("refX",(r?.9*v:1.1*v)+"px"),m.setAttribute("refY",w+"px"),m.setAttribute("markerWidth",2*v+"px"),m.setAttribute("markerHeight",2*w+"px"),m.setAttribute("orient","auto"),m.setAttribute("markerUnits","userSpaceOnUse");var x=J("path"),L=r?["M ".concat(2*v,",0"),"L 0,".concat(w),"L ".concat(2*v,",").concat(2*w),"Z"].join(" "):["M 0,0","L ".concat(2*v,",").concat(w),"L 0,".concat(2*w),"Z"].join(" ");x.setAttribute("d",L),x.setAttribute("fill",S(h)||"transparent"),m.appendChild(x),b.appendChild(m),e.appendChild(b),a.setAttribute(r?"marker-start":"marker-end","url(#".concat(g,")"))}function ot(t,e,a,r){void 0===r&&(r=!1);var n=t.border,o=void 0===n?{}:n,c=t.uuid,i=o.headEnd,s=o.width,h=o.color,l=o.tailEnd,p=r?i:l,d=p.len,u=void 0===d?"med":d,f=p.w,y=void 0===f?"med":f,v=at(u,s),w=at(y,s),b=J("defs"),m=J("marker"),g="marker-".concat(c,"-").concat(r?"start":"end");m.setAttribute("id",g),m.setAttribute("viewBox","0 0 ".concat(2*v," ").concat(2*w)),m.setAttribute("refX",v+"px"),m.setAttribute("refY",w+"px"),m.setAttribute("markerWidth",2*v+"px"),m.setAttribute("markerHeight",2*w+"px"),m.setAttribute("orient","auto"),m.setAttribute("markerUnits","userSpaceOnUse");var x=J("path"),L=["M 0,".concat(w),"L ".concat(v,",0"),"L ".concat(2*v,",").concat(w),"L ".concat(v,",").concat(2*w),"Z"].join(" ");x.setAttribute("d",L),x.setAttribute("fill",S(h)||"transparent"),m.appendChild(x),b.appendChild(m),e.appendChild(b),a.setAttribute(r?"marker-start":"marker-end","url(#".concat(g,")"))}function ct(t,e,a,r){void 0===r&&(r=!1);var n=t.border,o=void 0===n?{}:n,c=t.uuid,i=o.headEnd,s=o.width,h=o.color,l=o.tailEnd,p=r?i:l,d=p.len,u=void 0===d?"med":d,f=p.w,y=void 0===f?"med":f,v=at(u,s),w=at(y,s),b=J("defs"),m=J("marker"),g="marker-".concat(c,"-").concat(r?"start":"end");m.setAttribute("id",g),m.setAttribute("viewBox","0 0 ".concat(2*v+2*s," ").concat(2*w+2*s));var x=r?"lg"===y?2*s:3*s:"lg"===y?2*v:2*v-s;m.setAttribute("refX",x+"px"),m.setAttribute("refY",w+s+"px"),m.setAttribute("markerWidth",2*v+"px"),m.setAttribute("markerHeight",2*w+"px"),m.setAttribute("orient","auto"),m.setAttribute("markerUnits","userSpaceOnUse");var L=J("path"),M=r?["M ".concat(2*v+s,", ").concat(s),"L ".concat(s,",").concat(w+s),"L ".concat(2*v+s,",").concat(2*w+s)].join(" "):["M ".concat(s,", ").concat(s),"L ".concat(2*v+s,",").concat(w+s),"L ".concat(s,",").concat(2*w+s)].join(" ");L.setAttribute("d",M),L.setAttribute("stroke-width",s+"px"),L.setAttribute("stroke",S(h)||"transparent"),L.setAttribute("fill","transparent"),L.setAttribute("stroke-linecap","round"),L.setAttribute("stroke-linejoin","miter"),L.style.overflow="visible",m.appendChild(L),b.appendChild(m),e.appendChild(b),a.setAttribute(r?"marker-start":"marker-end","url(#".concat(g,")"))}function it(t,e,a,r){void 0===r&&(r=!1);var n=t.border,o=void 0===n?{}:n,c=t.uuid,i=o.headEnd,s=o.width,h=o.color,l=o.tailEnd,p=r?i:l,d=p.len,u=void 0===d?"med":d,f=p.w,y=void 0===f?"med":f,v=at(u,s),w=at(y,s),b=J("defs"),m=J("marker"),g="marker-".concat(c,"-").concat(r?"start":"end");m.setAttribute("id",g),m.setAttribute("viewBox","0 0 ".concat(2*v," ").concat(2*w));var x=r?"sm"===y?1.5*s:2*s:"sm"===y?2*v-1.5*s:2*v-2*s;m.setAttribute("refX",x+"px"),m.setAttribute("refY",w+"px"),m.setAttribute("markerWidth",2*v+"px"),m.setAttribute("markerHeight",2*w+"px"),m.setAttribute("orient","auto"),m.setAttribute("markerUnits","userSpaceOnUse");var L=J("path"),M=r?["M 0, ".concat(w),"L ".concat(2*v,",0"),"L ".concat(v,",").concat(w),"L ".concat(2*v,",").concat(2*w),"Z"].join(" "):["M 0,0","L ".concat(2*v,",").concat(w),"L 0,".concat(2*w),"L ".concat(v,",").concat(w),"Z"].join(" ");L.setAttribute("d",M),L.setAttribute("fill",S(h)||"transparent"),L.style.overflow="visible",m.appendChild(L),b.appendChild(m),e.appendChild(b),a.setAttribute(r?"marker-start":"marker-end","url(#".concat(g,")"))}function st(t,e,a){var r=t.border,n=void 0===r?{}:r;t.extend;var o=n.headEnd,c=n.tailEnd;if(o&&"none"!==o.type)switch(o.type){case"triangle":nt(t,e,a,!0);break;case"oval":rt(t,e,a,!0);break;case"diamond":ot(t,e,a,!0);break;case"arrow":ct(t,e,a,!0);break;case"stealth":it(t,e,a,!0)}if(c&&"none"!==c.type)switch(c.type){case"triangle":nt(t,e,a,!1);break;case"oval":rt(t,e,a,!1);break;case"diamond":ot(t,e,a,!1);break;case"arrow":ct(t,e,a,!1);break;case"stealth":it(t,e,a,!1)}}function ht(t){var e=t.extend,a=J("rect");return a.setAttribute("x","0"),a.setAttribute("y","0"),a.setAttribute("width",e.w+"px"),a.setAttribute("height",e.h+"px"),a}function lt(t,e,a,r,n){var o=t*Math.PI/180;return[0===o||o===2*Math.PI?e+r:o===Math.PI?e-r:o===Math.PI/2||o===3*Math.PI/2?e:o>0&&o<Math.PI/2||o>3*Math.PI/2&&o<2*Math.PI?e+Math.sqrt(1/(1/Math.pow(r,2)+Math.pow(Math.tan(o),2)/Math.pow(n,2))):e-Math.sqrt(1/(1/Math.pow(r,2)+Math.pow(Math.tan(o),2)/Math.pow(n,2))),0===o||o===2*Math.PI||o===Math.PI?a:o===Math.PI/2?a+n:o===3*Math.PI/2?a-n:o>Math.PI&&o<2*Math.PI?a-Math.sqrt(1/(1/Math.pow(n,2)+Math.pow(1/Math.tan(o),2)/Math.pow(r,2))):a+Math.sqrt(1/(1/Math.pow(n,2)+Math.pow(1/Math.tan(o),2)/Math.pow(r,2)))]}function pt(t,e){var a=0;return(e>t&&e-t>180||e<t&&t-e<180)&&(a=1),a}function dt(t){var e=t.extend,a=.146*e.w,r=.146*e.h;return{top:r,bottom:r,left:a,right:a,w:e.w-2*a,h:e.h-2*r}}function ut(e){var a=e.extend,r=e.offset,n=e.border,o=e.background,c=e.rotate,i=e.flipH,s=e.flipV,h=document.createElement("div"),l=r.x,p=r.y;h.className="shape-wrapper shape-".concat(e.shape),h.style.setProperty("position","absolute"),h.style.setProperty("width",(a.w||1)+"px"),h.style.setProperty("height",(a.h||1)+"px"),h.style.setProperty("left",l+"px"),h.style.setProperty("top",p+"px");var d,u=document.createElementNS(Y,"svg");u.style.setProperty("position","absolute"),u.setAttribute("width","100%"),u.setAttribute("height","100%"),u.style.setProperty("left","0"),u.style.setProperty("top","0"),u.style.overflow="visible";var f={left:0,top:0,right:0,bottom:0,w:a.w,h:a.h},y=!0;switch(e.shape){case"customGeom":d=function(t){var e=t.prstGeom,a=void 0===e?{}:e,r=t.extend,n=a.pathList,o=a.w,c=a.h,i=J("path"),s={moveTo:"M",lnTo:"L",cubicBezTo:"C",close:"Z"},h=r.w/o,l=r.h/c,p=n.map((function(t){var e=s[t.type],a=Array.isArray(t.points)?t.points.map((function(t){return"".concat(t[0]*h,",").concat(t[1]*l)})).join(" "):"";return a?"".concat(e," ").concat(a):"".concat(e)})).join(" ");return i.setAttribute("d",p),i.style.fillRule="evenodd",i}(e),st(e,u,d);break;case"flowChartProcess":case"rect":d=ht(e);break;case"snip1Rect":d=function(t){var e=t.extend,a=J("polygon"),r=tt("adj",t,K(t)),n=["0,0","".concat(e.w-r,",0"),"".concat(e.w,",").concat(r),"".concat(e.w,",").concat(e.h),"0,".concat(e.h)].join(" ");return a.setAttribute("points",n),a}(e);break;case"snip2SameRect":d=function(t){var e=t.extend,a=J("polygon"),r=tt("adj1",t,K(t)),n=tt("adj2",t,0),o=[[r,0],[e.w-r,0],[e.w,r],[e.w,e.h-n],[e.w-n,e.h],[n,e.h],[0,e.h-n],[0,r]].map((function(t){return"".concat(t[0],",").concat(t[1])})).join(" ");return a.setAttribute("points",o),a}(e);break;case"snip2DiagRect":d=function(t){var e=t.extend,a=J("polygon"),r=tt("adj1",t,0),n=tt("adj2",t,K(t)),o=[[r,0],[e.w-n,0],[e.w,n],[e.w,e.h-r],[e.w-r,e.h],[n,e.h],[0,e.h-n],[0,r]].map((function(t){return"".concat(t[0],",").concat(t[1])})).join(" ");return a.setAttribute("points",o),a}(e);break;case"snipRoundRect":d=function(t){var e=t.extend,a=J("path"),r=tt("adj1",t,K(t)),n=tt("adj2",t,K(t)),o=["M ".concat(r,",0"),"L ".concat(e.w-n,",0"),"L ".concat(e.w,",").concat(n),"L ".concat(e.w,",").concat(e.h),"L 0,".concat(e.h),"L 0,".concat(r),"Q 0,0 ".concat(r,",0"),"Z"].join(" ");return a.setAttribute("d",o),a}(e);break;case"roundRect":d=function(t){var e=ht(t),a=tt("adj",t,K(t));return e.setAttribute("rx",a+"px"),e.setAttribute("ry",a+"px"),e}(e);break;case"round1Rect":d=function(t){var e=t.extend,a=J("path"),r=tt("adj",t,K(t)),n=["M 0,0","L ".concat(e.w-r,",0"),"Q ".concat(e.w,",0 ").concat(e.w,",").concat(r),"L ".concat(e.w,",").concat(e.h),"L 0,".concat(e.h),"Z"].join(" ");return a.setAttribute("d",n),a}(e);break;case"round2SameRect":d=function(t){var e=t.extend,a=J("path"),r=tt("adj1",t,K(t)),n=tt("adj2",t,0),o=["M ".concat(r,",0"),"L ".concat(e.w-r,",0"),"Q ".concat(e.w,",0 ").concat(e.w,",").concat(r),"L ".concat(e.w,",").concat(e.h-n),"Q ".concat(e.w,",").concat(e.h," ").concat(e.w-n,",").concat(e.h),"L ".concat(n,",").concat(e.h),"Q 0,".concat(e.h," 0,").concat(e.h-n),"L 0,".concat(r),"Q 0,0 ".concat(r,",0"),"Z"].join(" ");return a.setAttribute("d",o),a}(e);break;case"round2DiagRect":d=function(t){var e=t.extend,a=J("path"),r=tt("adj1",t,K(t)),n=tt("adj2",t,0),o=["M ".concat(r,",0"),"L ".concat(e.w-n,",0"),"Q ".concat(e.w,",0 ").concat(e.w,",").concat(n),"L ".concat(e.w,",").concat(e.h-r),"Q ".concat(e.w,",").concat(e.h," ").concat(e.w-r,",").concat(e.h),"L ".concat(n,",").concat(e.h),"Q 0,".concat(e.h," 0,").concat(e.h-n),"L 0,".concat(r),"Q 0,0 ".concat(r,",0"),"Z"].join(" ");return a.setAttribute("d",o),a}(e);break;case"triangle":d=function(t){var e=t.extend,a=J("polygon"),r=["".concat(e.w/2,",0"),"0,".concat(e.h),"".concat(e.w,",").concat(e.h)].join(" ");return a.setAttribute("points",r),a}(e);break;case"rtTriangle":d=function(t){var e=t.extend,a=J("polygon"),r=["0,".concat(e.h),"0,0","".concat(e.w,",").concat(e.h)].join(" ");return a.setAttribute("points",r),a}(e);break;case"ellipse":d=function(t){var e=t.extend,a=J("ellipse"),r=e.w/2,n=e.h/2,o=r,c=n;return a.setAttribute("cx",r+"px"),a.setAttribute("cy",n+"px"),a.setAttribute("rx",o+"px"),a.setAttribute("ry",c+"px"),a}(e);break;case"line":d=function(t){var e=t.extend,a=J("path"),r=["M 0,0","L ".concat(e.w,",").concat(e.h)].join(" ");return a.setAttribute("d",r),a}(e),st(e,u,d),y=!1;break;case"straightConnector1":d=function(t){var e=t.extend,a=J("path"),r=["M 0,0","L ".concat(e.w,",").concat(e.h)].join(" ");return a.setAttribute("d",r),a}(e),st(e,u,d),y=!1;break;case"bentConnector3":d=function(t){var e=t.extend,a=J("path"),r=et("adj1",t,.5*Math.max(e.w,e.h)),n=["M 0,0","L ".concat(r,",0"),"L ".concat(r,",").concat(e.h),"L ".concat(e.w,",").concat(e.h)].join(" ");return a.setAttribute("d",n),a}(e),st(e,u,d),y=!1;break;case"curvedConnector3":d=function(t){var e=t.extend,a=J("path"),r=et("adj1",t,.5*Math.max(e.w,e.h)),n=["M0,0","Q".concat(r,",0 ").concat(r,",").concat(e.h/2),"T".concat(e.w,",").concat(e.h)].join(" ");return a.setAttribute("d",n),a}(e),st(e,u,d),y=!1;break;case"parallelogram":d=function(t){var e=t.extend,a=J("path"),r=tt("adj",t,.5*Math.min(e.w,e.h)),n=["M".concat(r,",0"),"L".concat(e.w,",0"),"L".concat(e.w-r,",").concat(e.h),"L0,".concat(e.h),"Z"].join(" ");return a.setAttribute("d",n),a}(e),f=function(t){var e=t.extend,a=tt("adj",t,.5*Math.min(e.w,e.h)),r=.84*(e.w-a),n=.08*e.h+a/e.w*e.h*.42;return{top:n,bottom:n,left:(e.w-r)/2,right:(e.w-r)/2,w:r,h:e.h-2*n}}(e);break;case"trapezoid":d=function(t){var e=t.extend,a=J("path"),r=tt("adj",t,.25*Math.min(e.w,e.h)),n=["M".concat(r,",0"),"L".concat(e.w-r,",0"),"L".concat(e.w,",").concat(e.h),"L0,".concat(e.h),"Z"].join(" ");return a.setAttribute("d",n),a}(e),f=function(t){var e=t.extend,a=tt("adj",t,.5*Math.min(e.w,e.h)),r=a/e.w*.66*e.h,n=.66*a;return{top:r,bottom:0,left:n,right:n,w:e.w-2*n,h:e.h-r}}(e);break;case"diamond":d=function(t){var e=t.extend,a=J("path"),r=["M".concat(e.w/2,",0"),"L".concat(e.w,",").concat(e.h/2),"L".concat(e.w/2,",").concat(e.h),"L0,".concat(e.h/2),"Z"].join(" ");return a.setAttribute("d",r),a}(e),f=function(t){var e=t.extend,a=.25*e.h,r=.25*e.w;return{top:a,bottom:a,left:r,right:r,w:.5*e.w,h:.5*e.h}}(e);break;case"pentagon":d=function(t){var e=t.extend,a=J("path"),r=["M".concat(e.w/2,",0"),"L".concat(e.w,",").concat(.3771*e.h),"L".concat(.808*e.w,",").concat(e.h),"L".concat(.192*e.w,",").concat(e.h),"L0,".concat(.3771*e.h),"Z"].join(" ");return a.setAttribute("d",r),a}(e),f=function(t){var e=t.extend,a=.227*e.h,r=.192*e.w;return{top:a,bottom:0,left:r,right:r,w:.616*e.w,h:.773*e.h}}(e);break;case"hexagon":d=function(t){var e=t.extend,a=J("path"),r=tt("adj",t,.25*Math.min(e.w,e.h)),n=["M".concat(r,",0"),"L".concat(e.w-r,",0"),"L".concat(e.w,",").concat(e.h/2),"L".concat(e.w-r,",").concat(e.h),"L".concat(r,",").concat(e.h),"L0,".concat(e.h/2),"Z"].join(" ");return a.setAttribute("d",n),a}(e),f=function(t){var e=t.extend,a=tt("adj",t,.25*Math.min(e.w,e.h)),r=.098*e.h+a/e.w*.38*e.h,n=.088*e.w+.422*a;return{top:r,bottom:r,left:n,right:n,w:e.w-2*n,h:e.h-2*r}}(e);break;case"heptagon":d=function(t){var e=t.extend,a=J("path"),r=["M".concat(e.w/2,",0"),"L".concat(.9*e.w,",").concat(.2*e.h),"L".concat(e.w,",").concat(.642*e.h),"L".concat(.722*e.w,",").concat(e.h),"L".concat(.278*e.w,",").concat(e.h),"L0,".concat(.642*e.h),"L".concat(.1*e.w,",").concat(.2*e.h),"Z"].join(" ");return a.setAttribute("d",r),a}(e),f=function(t){var e=t.extend;return{top:.2*e.h,bottom:.2*e.h,left:.1*e.w,right:.1*e.w,w:.8*e.w,h:.6*e.h}}(e);break;case"octagon":d=function(t){var e=t.extend,a=J("path"),r=tt("adj",t,.29*Math.min(e.w,e.h)),n=["M".concat(r,",0"),"L".concat(e.w-r,",0"),"L".concat(e.w,",").concat(r),"L".concat(e.w,",").concat(e.h-r),"L".concat(e.w-r,",").concat(e.h),"L".concat(r,",").concat(e.h),"L0,".concat(e.h-r),"L0,".concat(r),"Z"].join(" ");return a.setAttribute("d",n),a}(e),f=function(t){var e=t.extend,a=tt("adj",t,.29*Math.min(e.w,e.h));return{top:.5*a,bottom:.5*a,left:.5*a,right:.5*a,w:e.w-a,h:e.h-a}}(e);break;case"decagon":d=function(t){var e=t.extend,a=J("path"),r=.344,n=.117,o=.19,c=["M".concat(e.w*r,",0"),"L".concat(.656*e.w,",0"),"L".concat(.883*e.w,",").concat(e.h*o),"L".concat(e.w,",").concat(.5*e.h),"L".concat(.883*e.w,",").concat(.81*e.h),"L".concat(.656*e.w,",").concat(e.h),"L".concat(e.w*r,",").concat(e.h),"L".concat(e.w*n,",").concat(.81*e.h),"L0,".concat(.5*e.h),"L".concat(e.w*n,",").concat(e.h*o),"Z"].join(" ");return a.setAttribute("d",c),a}(e),f=function(t){var e=t.extend,a=.117,r=.19;return{top:e.h*r,bottom:e.h*r,left:e.w*a,right:e.w*a,w:.766*e.w,h:.62*e.h}}(e);break;case"dodecagon":d=function(t){var e=t.extend,a=J("path"),r=.364,n=.133,o=.135,c=["M".concat(e.w*r,",0"),"L".concat(.636*e.w,",0"),"L".concat(.867*e.w,",").concat(e.h*o),"L".concat(e.w,",").concat(e.h*r),"L".concat(e.w,",").concat(.636*e.h),"L".concat(.867*e.w,",").concat(.865*e.h),"L".concat(.636*e.w,",").concat(e.h),"L".concat(e.w*r,",").concat(e.h),"L".concat(e.w*n,",").concat(.865*e.h),"L0,".concat(.636*e.h),"L0,".concat(e.h*r),"L".concat(e.w*n,",").concat(e.h*o),"Z"].join(" ");return a.setAttribute("d",c),a}(e),f=function(t){var e=t.extend,a=.133,r=.135;return{top:e.h*r,bottom:e.h*r,left:e.w*a,right:e.w*a,w:.734*e.w,h:.73*e.h}}(e);break;case"pie":d=function(t){var e=t.extend,a=J("path"),r=$("adj1",t,360),n=$("adj2",t,270),o=e.w/2,c=e.h/2,i=e.w/2,s=e.h/2,h=lt(r,i,s,o,c),l=h[0],p=h[1],d=lt(n,i,s,o,c),u=d[0],f=d[1],y=pt(r,n),v="M".concat(i,",").concat(s,", L").concat(l," ").concat(p," A ").concat(o," ").concat(c," 0 ").concat(y," ").concat(1," ").concat(u," ").concat(f," Z");return a.setAttribute("d",v),a}(e),f=dt(e);break;case"arc":d=function(t){var e=t.extend,a=J("path"),r=$("adj1",t,270),n=$("adj2",t,0),o=e.w/2,c=e.h/2,i=e.w/2,s=e.h/2,h=lt(r,i,s,o,c),l=h[0],p=h[1],d=lt(n,i,s,o,c),u=d[0],f=d[1],y=pt(r,n),v="M".concat(l,",").concat(p," A ").concat(o," ").concat(c," 0 ").concat(y," ").concat(1," ").concat(u," ").concat(f);return a.setAttribute("d",v),a}(e),y=!1;break;case"bracketPair":d=function(t){var e=t.extend,a=J("path"),r=tt("adj",t,.16667*Math.min(e.w,e.h)),n=["M".concat(r,",").concat(e.h),"Q".concat(0,",").concat(e.h," ").concat(0,",").concat(e.h-r),"L".concat(0,",").concat(r),"Q".concat(0,",").concat(0," ").concat(r,",").concat(0),"M".concat(e.w-r,",").concat(0),"Q".concat(e.w,",").concat(0," ").concat(e.w,",").concat(r),"L".concat(e.w,",").concat(e.h-r),"Q".concat(e.w,",").concat(e.h," ").concat(e.w-r,",").concat(e.h)].join(" ");return a.setAttribute("d",n),a}(e),f=function(t){var e=t.extend,a=.285*tt("adj",t,.16667*Math.min(e.w,e.h));return{top:a,bottom:a,left:a,right:a,w:e.w-2*a,h:e.h-2*a}}(e),y=!1;break;case"bracePair":d=function(t){var e=t.extend,a=J("path"),r=tt("adj",t,.083335*Math.min(e.w,e.h)),n=["M".concat(2*r,",").concat(e.h),"Q".concat(r,",").concat(e.h," ").concat(r,",").concat(e.h-r),"L".concat(r,",").concat(e.h/2+r),"Q".concat(r,",").concat(e.h/2," ").concat(0,",").concat(e.h/2),"Q".concat(r,",").concat(e.h/2," ").concat(r,",").concat(e.h/2-r),"L".concat(r,",").concat(r),"Q".concat(r,",").concat(0," ").concat(2*r,",").concat(0),"M".concat(e.w-2*r,",").concat(0),"Q".concat(e.w-r,",").concat(0," ").concat(e.w-r,",").concat(r),"L".concat(e.w-r,",").concat(e.h/2-r),"Q".concat(e.w-r,",").concat(e.h/2," ").concat(e.w,",").concat(e.h/2),"Q".concat(e.w-r,",").concat(e.h/2," ").concat(e.w-r,",").concat(e.h/2+r),"L".concat(e.w-r,",").concat(e.h-r),"Q".concat(e.w-r,",").concat(e.h," ").concat(e.w-2*r,",").concat(e.h)].join(" ");return a.setAttribute("d",n),a}(e),f=function(t){var e=t.extend,a=.285*tt("adj",t,.16667*Math.min(e.w,e.h));return{top:a,bottom:a,left:a,right:a,w:e.w-2*a,h:e.h-2*a}}(e),y=!1;break;case"chord":d=function(t){var e=t.extend,a=J("path"),r=$("adj1",t,45),n=$("adj2",t,270),o=e.w/2,c=e.h/2,i=e.w/2,s=e.h/2,h=lt(r,i,s,o,c),l=h[0],p=h[1],d=lt(n,i,s,o,c),u=d[0],f=d[1],y=pt(r,n),v="M".concat(l," ").concat(p," A ").concat(o," ").concat(c," 0 ").concat(y," ").concat(1," ").concat(u," ").concat(f," Z");return a.setAttribute("d",v),a}(e),f=dt(e);break;case"teardrop":d=function(t){var e=t.extend,a=J("path"),r=$("adj",t,1),n=e.w/2,o=e.h/2,c=e.w/2,i=e.h/2,s=lt(0,c,i,n,o),h=s[0],l=s[1],p=lt(270,c,i,n,o),d=p[0],u=p[1],f=pt(0,270),y="M".concat(h," ").concat(l," A ").concat(n," ").concat(o," 0 ").concat(f," ").concat(1," ").concat(d," ").concat(u),v=n*r,w=c+v,b=i-o*v/(e.w/2),m=(e.w/2+w)/2,g=(e.h/2+b)/2;return y+=" Q".concat(m,",0 ").concat(w,",").concat(b),y+=" Q".concat(e.w,",").concat(g," ").concat(c+n,",").concat(i),a.setAttribute("d",y),a}(e),f=dt(e);break;case"frame":d=function(t){var e=t.extend,a=J("path"),r=tt("adj1",t,.12*Math.min(e.w,e.h)),n=["M0,0","L".concat(e.w,",0"),"L".concat(e.w,",").concat(e.h),"L0,".concat(e.h),"Z","M".concat(r,",").concat(r),"L".concat(r,",").concat(e.h-r),"L".concat(e.w-r,",").concat(e.h-r),"L".concat(e.w-r,",").concat(r),"Z"].join(" ");return a.setAttribute("d",n),a}(e),f=function(t){var e=t.extend,a=tt("adj1",t,.12*Math.min(e.w,e.h));return{top:a,bottom:a,left:a,right:a,w:e.w-2*a,h:e.h-2*a}}(e);break;case"halfFrame":d=function(t){var e=t.extend,a=J("path"),r=tt("adj1",t,.333*Math.min(e.w,e.h)),n=Math.min(tt("adj2",t,.333*Math.min(e.w,e.h)),e.w*(1-r/e.h)),o=["M0,0","L".concat(e.w,",0"),"L".concat(e.w*(1-r/e.h),",").concat(r),"L".concat(n,",").concat(r),"L".concat(n,",").concat(e.h*(1-n/e.w)),"L0,".concat(e.h),"Z"].join(" ");return a.setAttribute("d",o),a}(e);break;case"corner":d=function(t){var e=t.extend,a=J("path"),r=tt("adj1",t,.5*Math.min(e.w,e.h)),n=tt("adj2",t,.5*Math.min(e.w,e.h)),o=["M0,0","L".concat(n,",0"),"L".concat(n,",").concat(e.h-r),"L".concat(e.w,",").concat(e.h-r),"L".concat(e.w,",").concat(e.h),"L0,".concat(e.h),"Z"].join(" ");return a.setAttribute("d",o),a}(e),f=function(t){var e=t.extend,a=tt("adj1",t,.5*Math.min(e.w,e.h));return{top:e.h-a,bottom:0,left:0,right:0,w:e.w,h:a}}(e);break;case"diagStripe":d=function(t){var e=t.extend,a=J("path"),r=tt("adj",t,.5*Math.min(e.w,e.h)),n=e.w*r/e.h,o=["M".concat(n,",0"),"L".concat(e.w,",0"),"L0,".concat(e.h),"L0,".concat(r),"Z"].join(" ");return a.setAttribute("d",o),a}(e),f=function(t){var e=t.extend,a=$("adj",t,.5),r=.5*(1-a)*e.h,n=.5*(1-a)*e.w;return{top:0,bottom:r,left:0,right:n,w:e.w-n,h:e.h-r}}(e);break;case"plus":d=function(t){var e=t.extend,a=J("path"),r=tt("adj",t,.25*Math.min(e.w,e.h)),n=["M".concat(r,",0"),"L".concat(e.w-r,",0"),"L".concat(e.w-r,",").concat(r),"L".concat(e.w,",").concat(r),"L".concat(e.w,",").concat(e.h-r),"L".concat(e.w-r,",").concat(e.h-r),"L".concat(e.w-r,",").concat(e.h),"L".concat(r,",").concat(e.h),"L".concat(r,",").concat(e.h-r),"L0,".concat(e.h-r),"L0,".concat(r),"L".concat(r,",").concat(r),"Z"].join(" ");return a.setAttribute("d",n),a}(e),f=function(t){var e=t.extend,a=tt("adj",t,.25*Math.min(e.w,e.h));return{top:a,bottom:a,left:0,right:0,w:e.w,h:e.h-2*a}}(e);break;case"plaque":d=function(t){var e=t.extend,a=J("path"),r=tt("adj",t,.16667*Math.min(e.w,e.h)),n=["M".concat(r,",0"),"L".concat(e.w-r,",0"),"Q".concat(e.w-r,",").concat(r," ").concat(e.w,",").concat(r),"L".concat(e.w,",").concat(e.h-r),"Q".concat(e.w-r,",").concat(e.h-r," ").concat(e.w-r,",").concat(e.h),"L".concat(r,",").concat(e.h),"Q".concat(r,",").concat(e.h-r," 0,").concat(e.h-r),"L0,".concat(r),"Q".concat(r,",").concat(r," ").concat(r,",0"),"Z"].join(" ");return a.setAttribute("d",n),a}(e),f=function(t){var e=t.extend,a=tt("adj",t,.16667*Math.min(e.w,e.h));return{top:.72*a,bottom:.72*a,left:.72*a,right:.72*a,w:e.w-1.44*a,h:e.w-1.44*a}}(e);break;case"can":d=function(t){var e=t.extend,a=t.background,r=J("g"),n=J("path"),o=tt("adj",t,.25*Math.min(e.w,e.h)),c=["M0,".concat(o/2),"L0,".concat(e.h-o/2),"A".concat(e.w/2,",").concat(o/2," 0 0 0 ").concat(e.w,",").concat(e.h-o/2),"L".concat(e.w,",").concat(o/2),"A".concat(e.w/2,",").concat(o/2," 0 0 1 0,").concat(o/2),"Z"].join(" ");n.setAttribute("d",c);var i=J("ellipse"),s=e.w/2,h=o/2,l=e.w/2,p=o/2;return i.setAttribute("cx",s+"px"),i.setAttribute("cy",h+"px"),i.setAttribute("rx",l+"px"),i.setAttribute("ry",p+"px"),"solidFill"===(null==a?void 0:a.type)&&i.setAttribute("fill",S(a,{light:.5})||"transparent"),r.appendChild(n),r.appendChild(i),r}(e),f=function(t){var e=t.extend,a=tt("adj",t,.25*Math.min(e.w,e.h));return{top:a,bottom:0,left:0,right:0,w:e.w,h:e.h-a}}(e);break;case"cube":d=function(t){var e=t.extend,a=t.background,r=tt("adj",t,.25*Math.min(e.w,e.h)),n=J("g"),o=J("path"),c=["M0,".concat(r),"L".concat(e.w-r,",").concat(r),"L".concat(e.w-r,",").concat(e.h),"L0,".concat(e.h),"Z"].join(" ");o.setAttribute("d",c);var i=J("path"),s=["M0,".concat(r),"L".concat(r,",0"),"L".concat(e.w,",0"),"L".concat(e.w-r,",").concat(r),"Z"].join(" ");i.setAttribute("d",s),"solidFill"===(null==a?void 0:a.type)&&i.setAttribute("fill",S(a,{light:.8})||"transparent");var h=J("path"),l=["M".concat(e.w,",0"),"L".concat(e.w-r,",").concat(r),"L".concat(e.w-r,",").concat(e.h),"L".concat(e.w,",").concat(e.h-r),"Z"].join(" ");return h.setAttribute("d",l),"solidFill"===(null==a?void 0:a.type)&&h.setAttribute("fill",S(a,{dark:.6})||"transparent"),n.appendChild(o),n.appendChild(i),n.appendChild(h),n}(e),f=function(t){var e=t.extend,a=tt("adj",t,.25*Math.min(e.w,e.h));return{top:a,bottom:0,left:0,right:a,w:e.w-a,h:e.h-a}}(e);break;case"bevel":d=function(t){var e=t.extend,a=t.background,r=tt("adj",t,.125*Math.min(e.w,e.h)),n=J("g"),o=J("path"),c=["M".concat(r,",").concat(r),"L".concat(e.w-r,",").concat(r),"L".concat(e.w-r,",").concat(e.h-r),"L".concat(r,",").concat(e.h-r),"Z"].join(" ");o.setAttribute("d",c);var i=J("path"),s=["M0,0","L".concat(r,",").concat(r),"L".concat(e.w-r,",").concat(r),"L".concat(e.w,",0"),"Z"].join(" ");i.setAttribute("d",s),"solidFill"===(null==a?void 0:a.type)&&i.setAttribute("fill",S(a,{light:.8})||"transparent");var h=J("path"),l=["M".concat(e.w,",0"),"L".concat(e.w-r,",").concat(r),"L".concat(e.w-r,",").concat(e.h-r),"L".concat(e.w,",").concat(e.h),"Z"].join(" ");h.setAttribute("d",l),"solidFill"===(null==a?void 0:a.type)&&h.setAttribute("fill",S(a,{dark:.6})||"transparent");var p=J("path"),d=["M".concat(e.w,",").concat(e.h),"L".concat(e.w-r,",").concat(e.h-r),"L".concat(r,",").concat(e.h-r),"L0,".concat(e.h),"Z"].join(" ");p.setAttribute("d",d),"solidFill"===(null==a?void 0:a.type)&&p.setAttribute("fill",S(a,{dark:.625})||"transparent");var u=J("path"),f=["M0,".concat(e.h),"L".concat(r,",").concat(e.h-r),"L".concat(r,",").concat(r),"L0,0","Z"].join(" ");return u.setAttribute("d",f),"solidFill"===(null==a?void 0:a.type)&&u.setAttribute("fill",S(a,{light:.6})||"transparent"),n.appendChild(o),n.appendChild(i),n.appendChild(h),n.appendChild(p),n.appendChild(u),n}(e),f=function(t){var e=t.extend,a=tt("adj",t,.125*Math.min(e.w,e.h));return{top:a,bottom:a,left:a,right:a,w:e.w-2*a,h:e.h-2*a}}(e);break;case"donut":d=function(t){var e=t.extend,a=tt("adj",t,.25*Math.min(e.w,e.h)),r=J("path"),n=["M0,".concat(e.h/2),"A".concat(e.w/2,",").concat(e.h/2," 0 1,1 0,").concat(e.h/2+1),"Z","M".concat(e.w-a,",").concat(e.h/2),"A".concat(e.w/2-a,",").concat(e.h/2-a," 0 1,0 ").concat(e.w-a,",").concat(e.h/2+1),"Z"].join(" ");return r.setAttribute("d",n),r}(e),f=dt(e);break;case"noSmoking":d=function(t){var e=t.extend,a=tt("adj",t,.25*Math.min(e.w,e.h)),r=J("path"),n=Math.atan(e.h/e.w),o=a/2/Math.sin(n),c=e.w/2,i=e.h/2,s=-1*e.h/e.w,h=e.h*o/e.w,l=e.w/2-a,p=e.h/2-a,d=-2*l*l*s*h,u=Math.sqrt(Math.pow(2*l*l*s*h,2)-4*(p*p+l*l*s*s)*l*l*(h*h-p*p)),f=2*(p*p+l*l*s*s),y=(d-u)/f,v=s*y+h,w=(d+u)/f,b=s*w+h,m=-e.h*o/e.w,g=-2*l*l*s*m,x=Math.sqrt(Math.pow(2*l*l*s*m,2)-4*(p*p+l*l*s*s)*l*l*(m*m-p*p)),L=2*(p*p+l*l*s*s),M=(g-x)/L,A=s*M+m,P=(g+x)/L,k=s*P+m,j=["M0,".concat(e.h/2),"A".concat(e.w/2,",").concat(e.h/2," 0 1,1 0,").concat(e.h/2+1),"Z","M".concat(c+w,",").concat(i-b),"A".concat(l,",").concat(p," 0 0 0 ").concat(c+y,",").concat(i-v),"Z","M".concat(c+M,",").concat(i-A),"A".concat(l,",").concat(p," 0 0 0 ").concat(c+P,",").concat(i-k),"Z"].join(" ");return r.setAttribute("d",j),r}(e),f=dt(e);break;case"rightArrow":d=function(t){var e=t.extend,a=tt("adj1",t,.5*Math.min(e.w,e.h)),r=tt("adj2",t,.5*Math.min(e.w,e.h)),n=J("path"),o=["M0,".concat(e.h/2-a/2),"L".concat(e.w-r,",").concat(e.h/2-a/2),"L".concat(e.w-r,",0"),"L".concat(e.w,",").concat(e.h/2),"L".concat(e.w-r,",").concat(e.h),"L".concat(e.w-r,",").concat(e.h/2+a/2),"L0,".concat(e.h/2+a/2),"Z"].join(" ");return n.setAttribute("d",o),n}(e),f=function(t){var e=t.extend,a=tt("adj1",t,.5*Math.min(e.w,e.h)),r=a*tt("adj2",t,.5*Math.min(e.w,e.h))/e.h;return{top:e.h/2-a/2,bottom:e.h/2-a/2,left:0,right:r,w:e.w-r,h:a}}(e);break;case"leftArrow":d=function(t){var e=t.extend,a=tt("adj1",t,.5*Math.min(e.w,e.h)),r=tt("adj2",t,.5*Math.min(e.w,e.h)),n=J("path"),o=["M0,".concat(e.h/2),"L".concat(r,",0"),"L".concat(r,",").concat(e.h/2-a/2),"L".concat(e.w,",").concat(e.h/2-a/2),"L".concat(e.w,",").concat(e.h/2+a/2),"L".concat(r,",").concat(e.h/2+a/2),"L".concat(r,",").concat(e.h),"Z"].join(" ");return n.setAttribute("d",o),n}(e),f=function(t){var e=t.extend,a=tt("adj1",t,.5*Math.min(e.w,e.h)),r=a*tt("adj2",t,.5*Math.min(e.w,e.h))/e.h;return{top:e.h/2-a/2,bottom:e.h/2-a/2,left:r,right:0,w:e.w-r,h:a}}(e);break;case"upArrow":d=function(t){var e=t.extend,a=tt("adj1",t,.5*Math.min(e.w,e.h)),r=tt("adj2",t,.5*Math.min(e.w,e.h)),n=J("path"),o=["M".concat(e.w/2,",0"),"L".concat(e.w,",").concat(r),"L".concat(e.w/2+a/2,",").concat(r),"L".concat(e.w/2+a/2,",").concat(e.h),"L".concat(e.w/2-a/2,",").concat(e.h),"L".concat(e.w/2-a/2,",").concat(r),"L0,".concat(r),"Z"].join(" ");return n.setAttribute("d",o),n}(e),f=function(t){var e=t.extend,a=tt("adj1",t,.5*Math.min(e.w,e.h)),r=a*tt("adj2",t,.5*Math.min(e.w,e.h))/e.w;return{top:r,bottom:0,left:e.w/2-a/2,right:e.w/2-a/2,w:a,h:e.h-r}}(e);break;case"downArrow":d=function(t){var e=t.extend,a=tt("adj1",t,.5*Math.min(e.w,e.h)),r=tt("adj2",t,.5*Math.min(e.w,e.h)),n=J("path"),o=["M".concat(e.w/2,",").concat(e.h),"L0,".concat(e.h-r),"L".concat(e.w/2-a/2,",").concat(e.h-r),"L".concat(e.w/2-a/2,",0"),"L".concat(e.w/2+a/2,",0"),"L".concat(e.w/2+a/2,",").concat(e.h-r),"L".concat(e.w,",").concat(e.h-r),"Z"].join(" ");return n.setAttribute("d",o),n}(e),f=function(t){var e=t.extend,a=tt("adj1",t,.5*Math.min(e.w,e.h)),r=a*tt("adj2",t,.5*Math.min(e.w,e.h))/e.w;return{top:0,bottom:r,left:e.w/2-a/2,right:e.w/2-a/2,w:a,h:e.h-r}}(e);break;case"leftRightArrow":d=function(t){var e=t.extend,a=tt("adj1",t,.5*Math.min(e.w,e.h)),r=tt("adj2",t,.5*Math.min(e.w,e.h)),n=J("path"),o=["M0,".concat(e.h/2),"L".concat(r,",0"),"L".concat(r,",").concat(e.h/2-a/2),"L".concat(e.w-r,",").concat(e.h/2-a/2),"L".concat(e.w-r,",0"),"L".concat(e.w,",").concat(e.h/2),"L".concat(e.w-r,",").concat(e.h),"L".concat(e.w-r,",").concat(e.h/2+a/2),"L".concat(r,",").concat(e.h/2+a/2),"L".concat(r,",").concat(e.h),"Z"].join(" ");return n.setAttribute("d",o),n}(e),f=function(t){var e=t.extend,a=tt("adj1",t,.5*Math.min(e.w,e.h)),r=a*tt("adj2",t,.5*Math.min(e.w,e.h))/e.h;return{top:e.h/2-a/2,bottom:e.h/2-a/2,left:r,right:r,w:e.w-2*r,h:a}}(e);break;case"upDownArrow":d=function(t){var e=t.extend,a=tt("adj1",t,.5*Math.min(e.w,e.h)),r=tt("adj2",t,.5*Math.min(e.w,e.h)),n=J("path"),o=["M".concat(e.w/2,",").concat(e.h),"L0,".concat(e.h-r),"L".concat(e.w/2-a/2,",").concat(e.h-r),"L".concat(e.w/2-a/2,",").concat(r),"L0,".concat(r),"L".concat(e.w/2,",0"),"L".concat(e.w,",").concat(r),"L".concat(e.w/2+a/2,",").concat(r),"L".concat(e.w/2+a/2,",").concat(e.h-r),"L".concat(e.w,",").concat(e.h-r),"Z"].join(" ");return n.setAttribute("d",o),n}(e),f=function(t){var e=t.extend,a=tt("adj1",t,.5*Math.min(e.w,e.h)),r=a*tt("adj2",t,.5*Math.min(e.w,e.h))/e.w;return{top:r,bottom:r,left:e.w/2-a/2,right:e.w/2-a/2,w:a,h:e.h-2*r}}(e);break;case"quadArrow":d=function(t){var e=t.extend,a=tt("adj1",t,.225*Math.min(e.w,e.h)),r=tt("adj2",t,.225*Math.min(e.w,e.h)),n=tt("adj3",t,.225*Math.min(e.w,e.h)),o=J("path"),c=["M0,".concat(e.h/2),"L".concat(n,",").concat(e.h/2-r),"L".concat(n,",").concat(e.h/2-a/2),"L".concat(e.w/2-a/2,",").concat(e.h/2-a/2),"L".concat(e.w/2-a/2,",").concat(n),"L".concat(e.w/2-r,",").concat(n),"L".concat(e.w/2,",0"),"L".concat(e.w/2+r,",").concat(n),"L".concat(e.w/2+a/2,",").concat(n),"L".concat(e.w/2+a/2,",").concat(e.h/2-a/2),"L".concat(e.w-n,",").concat(e.h/2-a/2),"L".concat(e.w-n,",").concat(e.h/2-r),"L".concat(e.w,",").concat(e.h/2),"L".concat(e.w-n,",").concat(e.h/2+r),"L".concat(e.w-n,",").concat(e.h/2+a/2),"L".concat(e.w/2+a/2,",").concat(e.h/2+a/2),"L".concat(e.w/2+a/2,",").concat(e.h-n),"L".concat(e.w/2+r,",").concat(e.h-n),"L".concat(e.w/2,",").concat(e.h),"L".concat(e.w/2-r,",").concat(e.h-n),"L".concat(e.w/2-a/2,",").concat(e.h-n),"L".concat(e.w/2-a/2,",").concat(e.h/2+a/2),"L".concat(n,",").concat(e.h/2+a/2),"L".concat(n,",").concat(e.h/2+r),"Z"].join(" ");return o.setAttribute("d",c),o}(e),f=function(t){var e=t.extend,a=tt("adj1",t,.225*Math.min(e.w,e.h)),r=tt("adj2",t,.225*Math.min(e.w,e.h)),n=tt("adj3",t,.225*Math.min(e.w,e.h)),o=0===r?0:a*n/r/2;return{top:e.h/2-a/2,bottom:e.h/2-a/2,left:o,right:o,w:e.w-2*o,h:a}}(e);break;case"leftRightUpArrow":d=function(t){var e=t.extend,a=tt("adj1",t,.225*Math.min(e.w,e.h)),r=tt("adj2",t,.225*Math.min(e.w,e.h)),n=tt("adj3",t,.225*Math.min(e.w,e.h)),o=J("path");a>2*r&&(a=2*r);var c=["M0,".concat(e.h-r),"L".concat(n,",").concat(e.h-2*r),"L".concat(n,",").concat(e.h-r-a/2),"L".concat(e.w/2-a/2,",").concat(e.h-r-a/2),"L".concat(e.w/2-a/2,",").concat(n),"L".concat(e.w/2-r,",").concat(n),"L".concat(e.w/2,",0"),"L".concat(e.w/2+r,",").concat(n),"L".concat(e.w/2+a/2,",").concat(n),"L".concat(e.w/2+a/2,",").concat(e.h-r-a/2),"L".concat(e.w-n,",").concat(e.h-r-a/2),"L".concat(e.w-n,",").concat(e.h-2*r),"L".concat(e.w,",").concat(e.h-r),"L".concat(e.w-n,",").concat(e.h),"L".concat(e.w-n,",").concat(e.h-r+a/2),"L".concat(n,",").concat(e.h-r+a/2),"L".concat(n,",").concat(e.h),"Z"].join(" ");return o.setAttribute("d",c),o}(e),f=function(t){var e=t.extend,a=tt("adj1",t,.225*Math.min(e.w,e.h)),r=tt("adj2",t,.225*Math.min(e.w,e.h)),n=tt("adj3",t,.225*Math.min(e.w,e.h)),o=0===r?0:a*n/r/2;return{top:e.h-r-a/2,bottom:r-a/2,left:o,right:o,w:e.w-2*o,h:a}}(e);break;case"bentArrow":d=function(t){var e=t.extend,a=tt("adj1",t,.25*Math.min(e.w,e.h)),r=tt("adj2",t,.25*Math.min(e.w,e.h)),n=tt("adj3",t,.25*Math.min(e.w,e.h)),o=tt("adj4",t,.4375*Math.min(e.w,e.h)),c=J("path");a>2*r&&(a=2*r);var i=o-a;i<0&&(i=0);var s=["M0,".concat(e.h),"L0,".concat(r-a/2+o),"A".concat(o," ").concat(o," 0 0 1 ").concat(o," ").concat(r-a/2),"L".concat(e.w-n,",").concat(r-a/2),"L".concat(e.w-n,",0"),"L".concat(e.w,",").concat(r),"L".concat(e.w-n,",").concat(2*r),"L".concat(e.w-n,",").concat(r+a/2),"L".concat(a+i,",").concat(r+a/2),"A".concat(i," ").concat(i,"  0 0 0 ").concat(a," ").concat(r+a/2+i),"L".concat(a,",").concat(e.h),"Z"].join(" ");return c.setAttribute("d",s),c}(e);break;case"uturnArrow":d=function(t){var e=t.extend,a=tt("adj1",t,.25*Math.min(e.w,e.h)),r=tt("adj2",t,.25*Math.min(e.w,e.h)),n=tt("adj3",t,.25*Math.min(e.w,e.h)),o=tt("adj4",t,.4375*Math.min(e.w,e.h)),c=tt("adj5",t,.75*Math.min(e.w,e.h)),i=J("path");a>2*r&&(a=2*r),c<n&&(c=n+a),o>c-n&&(o=c-n);var s=o-a;s>c-n-a&&(s=c-n-a),s<0&&(s=0);var h=r-a/2,l=["M0,".concat(e.h),"L0,".concat(o),"A".concat(o," ").concat(o," 0 0 1 ").concat(o," 0"),"L".concat(e.w-o-h,",0"),"A".concat(o," ").concat(o," 0 0 1 ").concat(e.w-h," ").concat(o),"L".concat(e.w-h,",").concat(c-n),"L".concat(e.w,",").concat(c-n),"L".concat(e.w-r,",").concat(c),"L".concat(e.w-2*r,",").concat(c-n),"L".concat(e.w-r-a/2,",").concat(c-n),"L".concat(e.w-r-a/2,",").concat(a+s),"A".concat(s," ").concat(s,"  0 0 0 ").concat(e.w-s-r-a/2," ").concat(a),"L".concat(a+s,",").concat(a),"A".concat(s," ").concat(s,"  0 0 0 ").concat(a," ").concat(a+s),"L".concat(a,",").concat(e.h),"Z"].join(" ");return i.setAttribute("d",l),i}(e);break;case"leftUpArrow":d=function(t){var e=t.extend,a=tt("adj1",t,.25*Math.min(e.w,e.h)),r=tt("adj2",t,.25*Math.min(e.w,e.h)),n=tt("adj3",t,.25*Math.min(e.w,e.h));a>2*r&&(a=2*r);var o=Math.min(e.w,e.h)-2*r;n>o&&(n=o),n<0&&(n=0);var c=r-a/2,i=J("path"),s=["M0,".concat(e.h-r),"L".concat(n,",").concat(e.h-2*r),"L".concat(n,",").concat(e.h-r-a/2),"L".concat(e.w-r-a/2,",").concat(e.h-r-a/2),"L".concat(e.w-r-a/2,",").concat(n),"L".concat(e.w-2*r,",").concat(n),"L".concat(e.w-r,",0"),"L".concat(e.w,",").concat(n),"L".concat(e.w-c,",").concat(n),"L".concat(e.w-c,",").concat(e.h-c),"L".concat(n,",").concat(e.h-c),"L".concat(n,",").concat(e.h),"Z"].join(" ");return i.setAttribute("d",s),i}(e),f=function(t){var e=t.extend,a=tt("adj1",t,.25*Math.min(e.w,e.h)),r=tt("adj2",t,.25*Math.min(e.w,e.h)),n=tt("adj3",t,.25*Math.min(e.w,e.h));a>2*r&&(a=2*r);var o=Math.min(e.w,e.h)-2*r;n>o&&(n=o),n<0&&(n=0);var c=0===r?0:a*n/r/2;return{top:e.h-r-a/2,bottom:r-a/2,left:c,right:r,w:e.w-c-r,h:a}}(e);break;case"bentUpArrow":d=function(t){var e=t.extend,a=tt("adj1",t,.25*Math.min(e.w,e.h)),r=tt("adj2",t,.25*Math.min(e.w,e.h)),n=tt("adj3",t,.25*Math.min(e.w,e.h));a>2*r&&(a=2*r);var o=Math.min(e.w,e.h)-2*r;n>o&&(n=o),n<0&&(n=0);var c=r-a/2,i=J("path"),s=["M0,".concat(e.h),"L0,".concat(e.h-a),"L".concat(e.w-r-a/2,",").concat(e.h-a),"L".concat(e.w-r-a/2,",").concat(n),"L".concat(e.w-2*r,",").concat(n),"L".concat(e.w-r,",0"),"L".concat(e.w,",").concat(n),"L".concat(e.w-c,",").concat(n),"L".concat(e.w-c,",").concat(e.h),"Z"].join(" ");return i.setAttribute("d",s),i}(e),f=function(t){var e=t.extend,a=tt("adj1",t,.25*Math.min(e.w,e.h));return{top:e.h-a,bottom:0,left:0,right:0,w:e.w,h:a}}(e);break;case"curvedRightArrow":d=function(t){var e=t.extend,a=t.background,r=tt("adj1",t,.25*Math.min(e.w,e.h)),n=tt("adj2",t,.5*Math.min(e.w,e.h)),o=tt("adj3",t,.25*Math.min(e.w,e.h));r>n&&(r=n);var c=n/2-r/2,i=(e.h-n/2-r/2)/2,s=(e.h-c-r)/2,h=J("g"),l=J("path"),p=["M".concat(e.w,",0"),"A ".concat(e.w," ").concat(i," 0 0 0 ").concat(0," ").concat(i),"L0,".concat(s+r),"A ".concat(e.w," ").concat(s," 0 0 1 ").concat(e.w," ").concat(r),"Z"].join(" ");l.setAttribute("d",p),"solidFill"===(null==a?void 0:a.type)&&l.setAttribute("fill",S(a,{dark:.6})||"transparent");var d=J("path"),u=["M0,".concat(i),"A ".concat(e.w," ").concat(i," 0 0 0 ").concat(e.w-o," ").concat(e.h-n/2-r/2),"L".concat(e.w-o,",").concat(e.h-n),"L".concat(e.w,",").concat(e.h-n/2),"L".concat(e.w-o,",").concat(e.h),"L".concat(e.w-o,",").concat(e.h-c),"A ".concat(e.w," ").concat(s," 0 0 1 0 ").concat(r+s),"Z"].join(" ");return d.setAttribute("d",u),h.appendChild(l),h.appendChild(d),h}(e);break;case"curvedLeftArrow":d=function(t){var e=t.extend,a=t.background,r=tt("adj1",t,.25*Math.min(e.w,e.h)),n=tt("adj2",t,.5*Math.min(e.w,e.h)),o=tt("adj3",t,.25*Math.min(e.w,e.h));r>n&&(r=n);var c=n/2-r/2,i=(e.h-n/2-r/2)/2,s=(e.h-c-r)/2,h=J("g"),l=J("path"),p=["M0,".concat(e.h-n/2),"L".concat(o,",").concat(e.h-n),"L".concat(o,",").concat(e.h-n/2-r/2),"A".concat(e.w," ").concat(i," 0 0 0 ").concat(e.w," ").concat(i),"L".concat(e.w,",").concat(s+r),"A ".concat(e.w," ").concat(s," 0 0 1 ").concat(o," ").concat(e.h-c),"L".concat(o,",").concat(e.h),"Z"].join(" ");l.setAttribute("d",p);var d=J("path"),u=["M0,0","A ".concat(e.w," ").concat(i," 0 0 1 ").concat(e.w," ").concat(i),"L".concat(e.w,",").concat(s+r),"A ".concat(e.w," ").concat(s," 0 0 0 ").concat(0," ").concat(r),"Z"].join(" ");return d.setAttribute("d",u),"solidFill"===(null==a?void 0:a.type)&&d.setAttribute("fill",S(a,{dark:.6})||"transparent"),h.appendChild(l),h.appendChild(d),h}(e);break;case"curvedUpArrow":d=function(t){var e=t.extend,a=t.background,r=tt("adj1",t,.25*Math.min(e.w,e.h)),n=tt("adj2",t,.5*Math.min(e.w,e.h)),o=tt("adj3",t,.25*Math.min(e.w,e.h));r>n&&(r=n);var c=n/2-r/2,i=(e.w-n/2-r/2)/2,s=(e.w-c-r)/2,h=J("g"),l=J("path"),p=["M".concat(e.w-n/2,",").concat(0),"L".concat(e.w-n,",").concat(o),"L".concat(e.w-n/2-r/2,",").concat(o),"A".concat(i," ").concat(e.h," 0 0 1 ").concat(i," ").concat(e.h),"L".concat(i+r,",").concat(e.h),"A".concat(s," ").concat(e.h," 0 0 0 ").concat(e.w-c," ").concat(o),"L".concat(e.w,",").concat(o),"Z"].join(" ");l.setAttribute("d",p);var d=J("path"),u=["M".concat(r,",0"),"L".concat(0,",").concat(0),"A ".concat(i," ").concat(e.h," 0 0 0 ").concat(i," ").concat(e.h),"L".concat(i+r,",").concat(e.h),"A ".concat(s," ").concat(e.h," 0 0 1 ").concat(r," ").concat(0),"Z"].join(" ");return d.setAttribute("d",u),"solidFill"===(null==a?void 0:a.type)&&d.setAttribute("fill",S(a,{dark:.6})||"transparent"),h.appendChild(l),h.appendChild(d),h}(e);break;case"curvedDownArrow":d=function(t){var e=t.extend,a=t.background,r=tt("adj1",t,.25*Math.min(e.w,e.h)),n=tt("adj2",t,.5*Math.min(e.w,e.h)),o=tt("adj3",t,.25*Math.min(e.w,e.h));r>n&&(r=n);var c=n/2-r/2,i=(e.w-n/2-r/2)/2,s=(e.w-c-r)/2,h=J("g"),l=J("path"),p=["M".concat(0,",").concat(e.h),"L".concat(r,",").concat(e.h),"A".concat(s," ").concat(e.h," 0 0 1 ").concat(s+r," ").concat(0),"L".concat(i,",").concat(0),"A".concat(i," ").concat(e.h," 0 0 0 ").concat(0," ").concat(e.h),"Z"].join(" ");l.setAttribute("d",p),"solidFill"===(null==a?void 0:a.type)&&l.setAttribute("fill",S(a,{dark:.6})||"transparent");var d=J("path"),u=["M".concat(e.w-n/2,",").concat(e.h),"L".concat(e.w-n,",").concat(e.h-o),"L".concat(e.w-n/2-r/2,",").concat(e.h-o),"A ".concat(i," ").concat(e.h," 0 0 0 ").concat(i," ").concat(0),"L".concat(i+r,",").concat(0),"A ".concat(s," ").concat(e.h," 0 0 1 ").concat(e.w-c," ").concat(e.h-o),"L".concat(e.w,",").concat(e.h-o),"Z"].join(" ");return d.setAttribute("d",u),h.appendChild(l),h.appendChild(d),h}(e);break;case"stripedRightArrow":d=function(t){var e=t.extend,a=J("g"),r=Math.min(e.w,e.h),n=$("adj1",t,.5)*e.h,o=$("adj2",t,.5)*r,c=r/8,i=r/16,s=r/32,h=5*r/32,l=e.h/2-n/2,p=e.h/2+n/2,d=J("path");d.setAttribute("d",["M".concat(0,",").concat(l),"L".concat(s,",").concat(l),"L".concat(s,",").concat(p),"L".concat(0,",").concat(p),"Z"].join(" "));var u=J("path");u.setAttribute("d",["M".concat(i,",").concat(l),"L".concat(c,",").concat(l),"L".concat(c,",").concat(p),"L".concat(i,",").concat(p),"Z"].join(" "));var f=J("path"),y=["M".concat(h,",").concat(l),"L".concat(e.w-o,",").concat(l),"L".concat(e.w-o,",").concat(0),"L".concat(e.w,",").concat(e.h/2),"L".concat(e.w-o,",").concat(e.h),"L".concat(e.w-o,",").concat(p),"L".concat(h,",").concat(p),"Z"].join(" ");return f.setAttribute("d",y),a.appendChild(f),a.appendChild(d),a.appendChild(u),a}(e),f=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$("adj1",t,.5)*e.h,n=r*($("adj2",t,.5)*a)/e.h;return{top:e.h/2-r/2,bottom:e.h/2-r/2,left:0,right:n,w:e.w-n,h:r}}(e);break;case"notchedRightArrow":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$("adj1",t,.5)*e.h,n=$("adj2",t,.5)*a,o=r*n/e.h,c=e.h/2-r/2,i=e.h/2+r/2,s=J("path"),h=["M".concat(0,",").concat(c),"L".concat(e.w-n,",").concat(c),"L".concat(e.w-n,",").concat(0),"L".concat(e.w,",").concat(e.h/2),"L".concat(e.w-n,",").concat(e.h),"L".concat(e.w-n,",").concat(i),"L".concat(0,",").concat(i),"L".concat(o,",").concat(e.h/2),"Z"].join(" ");return s.setAttribute("d",h),s}(e),f=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$("adj1",t,.5)*e.h,n=$("adj2",t,.5)*a,o=r*n/e.h,c=r*n/e.h;return{top:e.h/2-r/2,bottom:e.h/2-r/2,left:o,right:c,w:e.w-o-c,h:r}}(e);break;case"homePlate":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$("adj",t,.5)*a,n=J("path"),o=["M".concat(0,",").concat(0),"L".concat(e.w-r,",").concat(0),"L".concat(e.w,",").concat(e.h/2),"L".concat(e.w-r,",").concat(e.h),"L".concat(0,",").concat(e.h),"Z"].join(" ");return n.setAttribute("d",o),n}(e),f=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$("adj",t,.5)*a;return{top:0,bottom:0,left:0,right:r/2,w:e.w-r/2,h:e.h}}(e);break;case"chevron":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$("adj",t,.5)*a,n=J("path"),o=["M".concat(0,",").concat(0),"L".concat(e.w-r,",").concat(0),"L".concat(e.w,",").concat(e.h/2),"L".concat(e.w-r,",").concat(e.h),"L".concat(0,",").concat(e.h),"L".concat(r,",").concat(e.h/2),"Z"].join(" ");return n.setAttribute("d",o),n}(e),f=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$("adj",t,.5)*a;return{top:0,bottom:0,left:r,right:r,w:e.w-2*r,h:e.h}}(e);break;case"blockArc":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=J("path"),n=$("adj1",t,180),o=$("adj2",t,0),c=tt("adj3",t,.25*a),i=e.w/2,s=e.h/2,h=e.w/2,l=e.h/2,p=e.w/2-c,d=e.h/2-c,u=lt(n,i,s,h,l),f=u[0],y=u[1],v=lt(o,i,s,h,l),w=v[0],b=v[1],m=lt(n,i,s,p,d),g=m[0],x=m[1],L=lt(o,i,s,p,d),M=L[0],A=L[1],P=pt(n,o),k=["M".concat(f,",").concat(y),"A".concat(h," ").concat(l," 0 ").concat(P," 1 ").concat(w," ").concat(b),"L".concat(M,",").concat(A),"A".concat(p," ").concat(d," 0 ").concat(P," 0 ").concat(g," ").concat(x),"Z"].join(" ");return r.setAttribute("d",k),r}(e),f=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$("adj1",t,180),n=$("adj2",t,0),o=tt("adj3",t,.25*a),c=e.w/2,i=e.h/2,s=e.w/2,h=e.h/2,l=e.w/2-o,p=e.h/2-o,d=lt(r,c,i,s,h),u=d[0],f=d[1],y=lt(n,c,i,s,h),v=y[0],w=y[1],b=lt(r,c,i,l,p),m=b[0],g=b[1],x=lt(n,c,i,l,p),L=[[u,f],[v,w],[m,g],[x[0],x[1]]];L.push(),r>n&&L.push([e.w,e.h/2]),(n>180&&n<=360&&r<180||r>n&&n>=0&&n<180&&r<180)&&L.push([0,e.h/2]),(r<n&&r<90&&n>90||r>n&&n>90||r>n&&r<90)&&L.push([e.w/2,e.h]),(r<n&&r<270&&n>270||r>n&&n>270||r>n&&r<270)&&L.push([e.w/2,0]);var M=1/0,A=1/0,P=-1/0,k=-1/0;return L.forEach((function(t){M=Math.min(t[0],M),A=Math.min(t[1],A),P=Math.max(t[0],P),k=Math.max(t[1],k)})),{top:A,bottom:e.h-k,left:M,right:e.w-P,w:P-M,h:k-A}}(e);break;case"foldedCorner":d=function(t){var e=t.extend,a=t.background,r=J("g"),n=J("path"),o=tt("adj",t,.16667*Math.min(e.w,e.h)),c=["M0,0","L".concat(e.w,",0"),"L".concat(e.w,",").concat(e.h-o),"L".concat(e.w-o,",").concat(e.h),"L".concat(0,",").concat(e.h),"Z"].join(" ");n.setAttribute("d",c);var i=o*Math.cos(Math.PI/4)/Math.cos(Math.PI/6)*Math.cos(75/180*Math.PI),s=J("path"),h=["M".concat(e.w-o+i,", ").concat(e.h-o+i),"L".concat(e.w,",").concat(e.h-o),"L".concat(e.w-o,",").concat(e.h),"Z"].join(" ");return s.setAttribute("d",h),"solidFill"===(null==a?void 0:a.type)&&s.setAttribute("fill",S(a,{dark:.6})||"transparent"),r.appendChild(n),r.appendChild(s),r}(e),f=function(t){var e=t.extend,a=tt("adj",t,.16667*Math.min(e.w,e.h));return{top:0,bottom:a,left:0,right:0,w:e.w,h:e.h-a}}(e);break;case"rightArrowCallout":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$("adj1",t,.25)*a,n=$("adj2",t,.25)*a,o=$("adj3",t,.25)*a,c=$("adj4",t,.64977)*e.w,i=J("path"),s=["M".concat(0,",").concat(0),"L".concat(c,",").concat(0),"L".concat(c,",").concat(e.h/2-r/2),"L".concat(e.w-o,",").concat(e.h/2-r/2),"L".concat(e.w-o,",").concat(e.h/2-n),"L".concat(e.w,",").concat(e.h/2),"L".concat(e.w-o,",").concat(e.h/2+n),"L".concat(e.w-o,",").concat(e.h/2+r/2),"L".concat(c,",").concat(e.h/2+r/2),"L".concat(c,",").concat(e.h),"L".concat(0,",").concat(e.h),"Z"].join(" ");return i.setAttribute("d",s),i}(e),f=function(t){var e=t.extend,a=$("adj4",t,.64977)*e.w;return{top:0,bottom:0,left:0,right:e.w-a,w:a,h:e.h}}(e);break;case"leftArrowCallout":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$("adj1",t,.25)*a,n=$("adj2",t,.25)*a,o=$("adj3",t,.25)*a,c=$("adj4",t,.64977)*e.w,i=J("path"),s=["M".concat(0,",").concat(e.h/2),"L".concat(o,",").concat(e.h/2-n),"L".concat(o,",").concat(e.h/2-r/2),"L".concat(e.w-c,",").concat(e.h/2-r/2),"L".concat(e.w-c,",").concat(0),"L".concat(e.w,",").concat(0),"L".concat(e.w,",").concat(e.h),"L".concat(e.w-c,",").concat(e.h),"L".concat(e.w-c,",").concat(e.h/2+r/2),"L".concat(o,",").concat(e.h/2+r/2),"L".concat(o,",").concat(e.h/2+n),"Z"].join(" ");return i.setAttribute("d",s),i}(e),f=function(t){var e=t.extend,a=$("adj4",t,.64977)*e.w;return{top:0,bottom:0,left:e.w-a,right:0,w:a,h:e.h}}(e);break;case"upArrowCallout":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$("adj1",t,.25)*a,n=$("adj2",t,.25)*a,o=$("adj3",t,.25)*a,c=$("adj4",t,.64977)*e.h,i=J("path"),s=["M".concat(0,",").concat(e.h-c),"L".concat(e.w/2-r/2,",").concat(e.h-c),"L".concat(e.w/2-r/2,",").concat(o),"L".concat(e.w/2-n,",").concat(o),"L".concat(e.w/2,",").concat(0),"L".concat(e.w/2+n,",").concat(o),"L".concat(e.w/2+r/2,",").concat(o),"L".concat(e.w/2+r/2,",").concat(e.h-c),"L".concat(e.w,",").concat(e.h-c),"L".concat(e.w,",").concat(e.h),"L".concat(0,",").concat(e.h),"Z"].join(" ");return i.setAttribute("d",s),i}(e),f=function(t){var e=t.extend,a=$("adj4",t,.64977)*e.h;return{top:e.h-a,bottom:0,left:0,right:0,w:e.w,h:a}}(e);break;case"downArrowCallout":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$("adj1",t,.25)*a,n=$("adj2",t,.25)*a,o=$("adj3",t,.25)*a,c=$("adj4",t,.64977)*e.h,i=J("path"),s=["M".concat(0,",").concat(0),"L".concat(e.w,",").concat(0),"L".concat(e.w,",").concat(c),"L".concat(e.w/2+r/2,",").concat(c),"L".concat(e.w/2+r/2,",").concat(e.h-o),"L".concat(e.w/2+n,",").concat(e.h-o),"L".concat(e.w/2,",").concat(e.h),"L".concat(e.w/2-n,",").concat(e.h-o),"L".concat(e.w/2-r/2,",").concat(e.h-o),"L".concat(e.w/2-r/2,",").concat(c),"L".concat(0,",").concat(c),"Z"].join(" ");return i.setAttribute("d",s),i}(e),f=function(t){var e=t.extend,a=$("adj4",t,.64977)*e.h;return{top:0,bottom:e.h-a,left:0,right:0,w:e.w,h:a}}(e);break;case"leftRightArrowCallout":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$("adj1",t,.25)*a,n=$("adj2",t,.25)*a,o=$("adj3",t,.25)*a,c=$("adj4",t,.48123)*e.w,i=J("path"),s=["M".concat(0,",").concat(e.h/2),"L".concat(o,",").concat(e.h/2-n),"L".concat(o,",").concat(e.h/2-r/2),"L".concat(e.w/2-c/2,",").concat(e.h/2-r/2),"L".concat(e.w/2-c/2,",").concat(0),"L".concat(e.w/2+c/2,",").concat(0),"L".concat(e.w/2+c/2,",").concat(e.h/2-r/2),"L".concat(e.w-o,",").concat(e.h/2-r/2),"L".concat(e.w-o,",").concat(e.h/2-n),"L".concat(e.w,",").concat(e.h/2),"L".concat(e.w-o,",").concat(e.h/2+n),"L".concat(e.w-o,",").concat(e.h/2+r/2),"L".concat(e.w/2+c/2,",").concat(e.h/2+r/2),"L".concat(e.w/2+c/2,",").concat(e.h),"L".concat(e.w/2-c/2,",").concat(e.h),"L".concat(e.w/2-c/2,",").concat(e.h/2+r/2),"L".concat(o,",").concat(e.h/2+r/2),"L".concat(o,",").concat(e.h/2+n),"Z"].join(" ");return i.setAttribute("d",s),i}(e),f=function(t){var e=t.extend,a=$("adj4",t,.48123)*e.w;return{top:0,bottom:0,left:e.w/2-a/2,right:e.w/2-a/2,w:a,h:e.h}}(e);break;case"quadArrowCallout":d=function(t){var e=t.extend,a=Math.min(e.w,e.h),r=$("adj1",t,.18515)*a,n=$("adj2",t,.18515)*a,o=$("adj3",t,.18515)*a,c=$("adj4",t,.48123)*e.w,i=$("adj4",t,.48123)*e.h,s=J("path"),h=["M".concat(0,",").concat(e.h/2),"L".concat(o,",").concat(e.h/2-n),"L".concat(o,",").concat(e.h/2-r/2),"L".concat(e.w/2-c/2,",").concat(e.h/2-r/2),"L".concat(e.w/2-c/2,",").concat(e.h/2-i/2),"L".concat(e.w/2-r/2,",").concat(e.h/2-i/2),"L".concat(e.w/2-r/2,",").concat(o),"L".concat(e.w/2-n,",").concat(o),"L".concat(e.w/2,",").concat(0),"L".concat(e.w/2+n,",").concat(o),"L".concat(e.w/2+r/2,",").concat(o),"L".concat(e.w/2+r/2,",").concat(e.h/2-i/2),"L".concat(e.w/2+c/2,",").concat(e.h/2-i/2),"L".concat(e.w/2+c/2,",").concat(e.h/2-r/2),"L".concat(e.w-o,",").concat(e.h/2-r/2),"L".concat(e.w-o,",").concat(e.h/2-n),"L".concat(e.w,",").concat(e.h/2),"L".concat(e.w-o,",").concat(e.h/2+n),"L".concat(e.w-o,",").concat(e.h/2+r/2),"L".concat(e.w/2+c/2,",").concat(e.h/2+r/2),"L".concat(e.w/2+c/2,",").concat(e.h/2+i/2),"L".concat(e.w/2+r/2,",").concat(e.h/2+i/2),"L".concat(e.w/2+r/2,",").concat(e.h-o),"L".concat(e.w/2+n,",").concat(e.h-o),"L".concat(e.w/2,",").concat(e.h),"L".concat(e.w/2-n,",").concat(e.h-o),"L".concat(e.w/2-r/2,",").concat(e.h-o),"L".concat(e.w/2-r/2,",").concat(e.h/2+i/2),"L".concat(e.w/2-c/2,",").concat(e.h/2+i/2),"L".concat(e.w/2-c/2,",").concat(e.h/2+r/2),"L".concat(o,",").concat(e.h/2+r/2),"L".concat(o,",").concat(e.h/2+n),"Z"].join(" ");return s.setAttribute("d",h),s}(e),f=function(t){var e=t.extend,a=$("adj4",t,.48123)*e.w,r=$("adj4",t,.48123)*e.h;return{top:e.h/2-r/2,bottom:e.h/2-r/2,left:e.w/2-a/2,right:e.w/2-a/2,w:a,h:r}}(e)}if(d){y?"blipFill"===(null==o?void 0:o.type)?function(t,e,a){var r=t.background,n=void 0===r?{}:r,o=t.extend,c=n.base64,i=n.alpha,s=n.fillRect,h=void 0===s?{}:s,l=h.b,p=void 0===l?0:l,d=h.t,u=void 0===d?0:d,f=h.l,y=void 0===f?0:f,v=h.r,w=void 0===v?0:v,b=J("defs"),m=J("pattern");m.setAttribute("id","background_"+t.uuid),m.setAttribute("patternUnits","userSpaceOnUse"),m.setAttribute("width",o.w+""),m.setAttribute("height",o.h+"");var g=J("image");g.setAttribute("href",c),g.setAttribute("preserveAspectRatio","none");var x=o.w*y,L=o.h*u,M=o.w*(1-y-w),A=o.h*(1-u-p);g.setAttribute("width",M+""),g.setAttribute("height",A+""),g.setAttribute("x",x+""),g.setAttribute("y",L+""),"number"==typeof i&&g.setAttribute("opacity",i+""),m.appendChild(g),b.appendChild(m),e.appendChild(b),a.setAttribute("fill","url(#background_".concat(t.uuid,")"))}(e,u,d):"gradFill"===(null==o?void 0:o.type)?function(t,e,a){var r=t.background,n=void 0===r?{}:r;t.extend;var o=n.gsList,c=n.lin,i=n.path,s=n.tileRect,h=void 0===s?{}:s,l=J("defs"),p=J("circle"===i?"radialGradient":"linearGradient");p.setAttribute("id","background_grad_fill_"+t.uuid);var d=o||[];if(d.sort((function(t,e){return t.pos-e.pos})),d.forEach((function(t){var e=J("stop");e.setAttribute("offset","".concat(100*t.pos,"%")),e.setAttribute("stop-color",S(t.color)),p.appendChild(e)})),"circle"===i){var u=h.r,f=h.l,y=h.t,v=h.b;-1===u?p.setAttribute("cx","100%"):-1===f&&p.setAttribute("cx","0%"),-1===y?p.setAttribute("cy","0%"):-1===v&&p.setAttribute("cy","100%")}else(null==c?void 0:c.ang)&&p.setAttribute("gradientTransform","rotate(".concat(c.ang,")"));l.appendChild(p),e.appendChild(l),a.setAttribute("fill","url(#background_grad_fill_".concat(t.uuid,")"))}(e,u,d):d.setAttribute("fill",S(o)||"transparent"):d.setAttribute("fill","transparent"),n.width?(d.setAttribute("stroke-width",n.width+"px"),d.setAttribute("stroke",S(n.color)||"transparent")):d.setAttribute("stroke-width","0"),d.setAttribute("stroke-dasharray",function(t){return t&&"solid"!==t.type?({sysDot:[1,1],sysDash:[3,1],dash:[4,3],dashDot:[4,3,1,3],lgDash:[8,3],lgDashDot:[8,3,1,3],lgDashDotDot:[8,3,1,3,1,3]}[t.type]||[]).map((function(e){return e*t.width})).join(","):""}(n));d.setAttribute("stroke-linecap",n.cap&&{sq:"square",rnd:"round",flat:"butt"}[n.cap]||"square"),d.setAttribute("stroke-linejoin",n.lineJoin||"round"),"miter"===n.lineJoin&&d.setAttribute("stroke-miterlimit",n.miterLim+""),u.appendChild(d)}var v=[];i&&v.push("scaleX(-1)"),s&&v.push("scaleY(-1)"),u.style.setProperty("transform",v.join(" ")),h.appendChild(u);var w=function(e,a,r){var n;void 0===r&&(r=!1);var o=e.inheritProps,c=e.props,i=e.paragraphs;if(!i||0===i.length)return null;var s=t(t({},o),c),h=document.createElement("div");switch(h.className="text-wrapper",h.style.boxSizing="border-box",h.style.position="absolute",h.style.left=a.left+"px","eaVert"===s.vert?(h.style.writingMode="vertical-rl",h.style.height=a.h+"px"):h.style.width=a.w+"px",s.anchor){case"b":h.style.bottom=a.bottom+"px";break;case"t":h.style.top=a.top+"px";break;case"ctr":h.style.top=a.top+a.h/2+"px",h.style.transform="translateY(-50%)";break;default:r?h.style.top=a.top+"px":(h.style.top=a.top+a.h/2+"px",h.style.transform="translateY(-50%)")}var l=[s.hasOwnProperty("tIns")?Math.floor(s.tIns)+"px":"3px",s.hasOwnProperty("rIns")?Math.floor(s.rIns)+"px":"5px",s.hasOwnProperty("bIns")?Math.floor(s.bIns)+"px":"3px",s.hasOwnProperty("lIns")?Math.floor(s.lIns)+"px":"5px"];h.style.padding=l.join(" ");for(var p={},d=0,u=function(e){var a=t(t({},e.inheritProps),e.props),r=void 0;if(a.buAutoNum){var o=a.level?+a.level:0;p[o]||(p[o]=0),(null===(n=e.rows)||void 0===n?void 0:n.length)&&p[o]++,Object.keys(p).forEach((function(t){+t>o&&(p[t]=0)})),r=U(e,p[o],{isFirst:0===d,isLast:i.length-1===d,bodyProps:s})}else r=U(e,0,{isFirst:0===d,isLast:i.length-1===d,bodyProps:s});h.appendChild(r),d++},f=0,y=i;f<y.length;f++)u(y[f]);return h}(e.textBody,f,e.isTextBox);return w&&h.appendChild(w),c&&h.style.setProperty("transform","rotate(".concat(c,"deg)")),h}function ft(t){var e=document.createElement("div"),a=t.extend;t.chExtend;var r=t.offset;t.chOffset;var n=t.flipV,o=t.flipH,c=t.rotate,i=r.x,s=r.y,h=a.w,l=a.h;e.className="group",e.style.position="absolute",e.style.left=i+"px",e.style.top=s+"px",e.style.width=h+"px",e.style.height=l+"px";var p=[];o&&p.push("scaleX(-1)"),n&&p.push("scaleY(-1)"),c&&p.push("rotate(".concat(c,"deg)")),e.style.transformOrigin="center center",e.style.transform=p.join(" ");for(var d=0;d<t.nodes.length;d++){var u=t.nodes[d],f=void 0;u instanceof R?f=V(u):u instanceof T?f=ut(u):u instanceof O&&(f=ft(u)),e.appendChild(f)}return e}var yt={};function vt(t,e){yt[t]||(yt[t]=[]),yt[t].push(e)}function wt(t,e){if(yt[t])for(var a=0;a<yt[t].length;a++)yt[t][a](e)}function bt(t,e){yt?yt[t]&&(yt[t]=yt[t].filter((function(t){return t!==e}))):yt[t]=[]}var mt=function(){function e(t,e,a){this.scale=1,this.wrapper=t,this.pptx=e,this.options=a,this._calcScaleAndRenderPort()}return e.prototype._calcScaleAndRenderPort=function(){var t=this.options.viewPort.width/this.pptx.width;this.scale=t;var e=this.options.viewPort.width,a=this.pptx.height*this.scale;this.renderPort={width:e,height:a,left:0,top:0}},e.prototype.renderSlide=function(t){var e=this.pptx.slides[t],a=document.createElement("div");a.classList.add("pptx-preview-slide-wrapper"),a.classList.add("pptx-preview-slide-wrapper-".concat(t)),a.style.setProperty("width",this.renderPort.width+"px"),a.style.setProperty("height",this.renderPort.height+"px"),a.style.setProperty("position","slide"===this.options.mode?"absolute":"relative"),"slide"===this.options.mode&&a.style.setProperty("top",(this.options.viewPort.height-this.renderPort.height)/2+"px"),a.style.margin="0 auto 10px",a.style.setProperty("background","#fff"),a.style.setProperty("overflow","hidden"),this._renderBackground(e,a),this._renderSlideMaster(e.slideMaster,a),this._renderSlideLayout(e.slideLayout,a),this._renderSlide(e,a),this.wrapper.append(a)},e.prototype._renderSlideMaster=function(t,e){var a=document.createElement("div");a.classList.add("slide-master-wrapper"),a.style.setProperty("position","absolute"),a.style.setProperty("left","0"),a.style.setProperty("top","0"),a.style.setProperty("width",this.pptx.width+"px"),a.style.setProperty("height",this.pptx.height+"px"),a.style.setProperty("transform","scale(".concat(this.scale,")")),a.style.setProperty("transform-origin","0 0");var r=n([],t.nodes,!0).filter((function(t){return t.userDrawn}));r.sort((function(t,e){return t.order>e.order?1:-1}));for(var o=0;o<r.length;o++){var c=this._renderNode(r[o]);c&&a.append(c)}e.append(a)},e.prototype._renderSlideLayout=function(t,e){var a=document.createElement("div");a.classList.add("slide-layout-wrapper"),a.style.setProperty("position","absolute"),a.style.setProperty("left","0"),a.style.setProperty("top","0"),a.style.setProperty("width",this.pptx.width+"px"),a.style.setProperty("height",this.pptx.height+"px"),a.style.setProperty("transform","scale(".concat(this.scale,")")),a.style.setProperty("transform-origin","0 0");var r=n([],t.nodes,!0).filter((function(t){return t.userDrawn}));r.sort((function(t,e){return t.order>e.order?1:-1}));for(var o=0;o<r.length;o++){var c=this._renderNode(r[o]);c&&a.append(c)}e.append(a)},e.prototype._renderSlide=function(t,e){var a=document.createElement("div");a.classList.add("slide-wrapper"),a.style.setProperty("position","absolute"),a.style.setProperty("left","0"),a.style.setProperty("top","0"),a.style.setProperty("width",this.pptx.width+"px"),a.style.setProperty("height",this.pptx.height+"px"),a.style.setProperty("transform","scale(".concat(this.scale,")")),a.style.setProperty("transform-origin","0 0");var r=n([],t.nodes,!0);r.sort((function(t,e){return t.order>e.order?1:-1}));for(var o=0;o<r.length;o++){var c=this._renderNode(r[o]);c&&a.append(c)}e.append(a)},e.prototype._renderNode=function(e){return e instanceof R?V(e):e instanceof T?ut(e):e instanceof O?ft(e):e instanceof E?function(t){var e=document.createElement("div"),a=t.extend,r=t.offset,n=t.flipV,o=t.flipH,c=t.rotate,i=r.x,s=r.y,h=a.w,l=a.h;e.className="smart-chart-diagram",e.style.position="absolute",e.style.left=i+"px",e.style.top=s+"px",e.style.width=h+"px",e.style.height=l+"px";var p=[];o&&p.push("scaleX(-1)"),n&&p.push("scaleY(-1)"),c&&p.push("rotate(".concat(c,"deg)")),e.style.transformOrigin="center center",e.style.transform=p.join(" ");for(var d=0;d<t.nodes.length;d++){var u=t.nodes[d],f=void 0;u instanceof R?f=V(u):u instanceof T&&(f=ut(u)),e.appendChild(f)}return e}(e):e instanceof _?function(e){var a=e.extend,r=e.offset,n=e.tr,o=e.tableGrid.gridCol,c=document.createElement("div");c.style.position="absolute",c.style.left=r.x+"px",c.style.top=r.y+"px",c.style.width=a.w+"px",c.style.height=a.h+"px";var i=document.createElement("table");return i.style.borderCollapse="collapse",n.forEach((function(e){var a=e.props,r=e.td,n=a.height,c=document.createElement("tr");c.style.height=n+"px",r.forEach((function(e,a){var r,n,i=e.props,s=e.inheritTcStyle,h=e.inheritTcTxStyle,l=e.paragraphs;if(!i.vMerge&&!i.hMerge){var p=document.createElement("td");p.style.width=((null===(r=o[a])||void 0===r?void 0:r.width)||30)+"px",i.rowSpan&&p.setAttribute("rowspan",i.rowSpan+""),i.gridSpan&&p.setAttribute("colspan",i.gridSpan+"");var d=i.background||s.background;d&&(p.style.background=S(d));var u=t(t({},s.border),i.border),f=function(t){return t?t.toLowerCase().includes("dash")?"dashed":t.toLowerCase().includes("dot")?"dotted":"solid":"solid"};u.bottom&&(p.style.borderBottom="".concat(u.bottom.width,"px ").concat(f(u.bottom.type)," ").concat(S(u.bottom.color))),u.top&&(p.style.borderTop="".concat(u.top.width,"px ").concat(f(u.top.type)," ").concat(S(u.top.color))),u.left&&(p.style.borderLeft="".concat(u.left.width,"px ").concat(f(u.left.type)," ").concat(S(u.left.color))),u.right&&(p.style.borderRight="".concat(u.right.width,"px ").concat(f(u.right.type)," ").concat(S(u.right.color)));var y=document.createElement("div");switch(y.className="text-wrapper",y.style.boxSizing="border-box",y.style.width="100%",y.style.height="100%",y.style.overflow="hidden",y.style.display="flex",y.style.flexDirection="column",i.anchor){case"b":y.style.justifyContent="flex-end";break;case"t":y.style.justifyContent="flex-start";break;case"ctr":y.style.justifyContent="center"}var v=[i.hasOwnProperty("marT")?Math.floor(i.marT)+"px":"3px",i.hasOwnProperty("marR")?Math.floor(i.marR-1)+"px":"5px",i.hasOwnProperty("marB")?Math.floor(i.marB)+"px":"3px",i.hasOwnProperty("marL")?Math.floor(i.marL-1)+"px":"5px"];y.style.padding=v.join(" ");for(var w=t({},i),b={},m=l.map((function(e){return t(t({},e),{props:t({align:"l"},e.props),inheritRProps:t(t({},e.inheritRProps),h)})})),g=0,x=function(e){var a=t(t({},e.inheritProps),e.props),r=void 0;if(a.buAutoNum){var o=a.level?+a.level:0;b[o]||(b[o]=0),(null===(n=e.rows)||void 0===n?void 0:n.length)&&b[o]++,Object.keys(b).forEach((function(t){+t>o&&(b[t]=0)})),r=U(e,b[o],{isFirst:0===g,isLast:l.length-1===g,bodyProps:w})}else r=U(e,0,{isFirst:0===g,isLast:l.length-1===g,bodyProps:w});y.appendChild(r),g++},L=0,M=m;L<M.length;L++)x(M[L]);p.append(y),c.append(p)}})),i.append(c)})),c.append(i),c}(e):e instanceof N?function(t){var e=document.createElement("div"),a=t.extend,r=t.offset,n=t.flipV,o=t.flipH,c=t.rotate,i=t.options,s=r.x,l=r.y,p=a.w,d=a.h;e.className="chart-node",e.style.position="absolute",e.style.left=s+"px",e.style.top=l+"px",e.style.width=p+"px",e.style.height=d+"px";var u=[];return o&&u.push("scaleX(-1)"),n&&u.push("scaleY(-1)"),c&&u.push("rotate(".concat(c,"deg)")),e.style.transformOrigin="center center",e.style.transform=u.join(" "),setTimeout((function(){var t=h.init(e,null,{renderer:"svg"});t.setOption(i),vt("destroy",(function(){t&&t.dispose(),t&&(t=null)})),vt("removeSlide",(function(){t&&t.dispose(),t&&(t=null)}))}),0),e}(e):void 0},e.prototype._renderBackground=function(t,e){var a,r=document.createElement("div");r.classList.add("slide-background"),r.style.setProperty("position","absolute"),r.style.setProperty("left","0"),r.style.setProperty("top","0"),r.style.setProperty("width","100%"),r.style.setProperty("height","100%");var n=t.background;if("none"===n.type&&(n=t.slideLayout.background),"none"===n.type&&(n=t.slideMaster.background),"blipFill"===n.type){var o=n,c=o.base64,i=o.alpha,s=o.fillRect,h=void 0===s?{}:s,l=h.b,p=void 0===l?0:l,d=h.t,u=void 0===d?0:d,f=h.l,y=void 0===f?0:f,v=h.r,w=void 0===v?0:v,b=this.renderPort.width*y,m=this.renderPort.height*u,g=this.renderPort.width*(1-y-w),x=this.renderPort.height*(1-u-p);r.style.backgroundImage="url(".concat(c,")"),r.style.backgroundSize="".concat(g," ").concat(x),r.style.backgroundPosition="".concat(b,"px ").concat(m,"px"),i&&(r.style.opacity=i+""),r.style.backgroundRepeat="no-repeat"}else if("solidFill"===n.type){var L=S(t.background)||S(t.slideLayout.background)||S(t.slideMaster.background);L?r.style.setProperty("background",L):r.style.setProperty("background","#fff")}else if("gradFill"===n.type)if("circle"===n.path){var M=n.tileRect||{},A=(p=M.b,u=M.t,y=M.l,"radial-gradient(circle at ");-1===(w=M.r)?A+=" right":-1===y&&(A+=" left"),-1===u?A+=" top":-1===p&&(A+=" bottom"),p||u||y||w||(A+=" center"),A+=",",A+=n.gsList.map((function(t){return"".concat(S(t.color)," ").concat(100*t.pos+"%")})).join(","),r.style.setProperty("background",A)}else{var P=(null===(a=null==n?void 0:n.lin)||void 0===a?void 0:a.ang)||0;A="linear-gradient(".concat(P+90,"deg,");A+=n.gsList.map((function(t){return"".concat(S(t.color)," ").concat(100*t.pos+"%")})).join(","),r.style.setProperty("background",A)}e.append(r)},e}(),gt=function(){function t(t,e){this.currentIndex=0,this.dom=t,this.options=e,this._renderWrapper()}return Object.defineProperty(t.prototype,"slideCount",{get:function(){var t,e;return null===(e=null===(t=this.pptx)||void 0===t?void 0:t.slides)||void 0===e?void 0:e.length},enumerable:!1,configurable:!0}),t.prototype._renderWrapper=function(){var t=document.createElement("div");t.classList.add("pptx-preview-wrapper"),t.setAttribute("position","relative"),t.style.setProperty("background","#000"),t.style.setProperty("width",this.options.width+"px"),this.options.height&&t.style.setProperty("height",this.options.height+"px"),t.style.setProperty("position","relative"),t.style.setProperty("margin","0 auto"),this.options.height&&t.style.setProperty("overflow-y","auto"),this.dom.append(t),this.wrapper=t},t.prototype.renderNextButton=function(){var t=document.createElement("div");t.classList.add("pptx-preview-wrapper-next"),t.style.setProperty("position","absolute"),t.style.setProperty("bottom","20px"),t.style.setProperty("right","80px"),t.style.setProperty("z-index","100"),t.style.setProperty("cursor","pointer"),t.style.setProperty("width","40px"),t.style.setProperty("height","40px"),t.style.setProperty("background","#666666"),t.style.setProperty("border-radius","100%");var e=document.createElement("div");return e.style.setProperty("width","10px"),e.style.setProperty("height","10px"),e.style.setProperty("border-left","2px solid #fff"),e.style.setProperty("border-bottom","2px solid #fff"),e.style.setProperty("transform","rotate(225deg)"),e.style.setProperty("position","absolute"),e.style.setProperty("top","15px"),e.style.setProperty("left","15px"),t.append(e),t},t.prototype.renderPreButton=function(){var t=document.createElement("div");t.classList.add("pptx-preview-wrapper-next"),t.style.setProperty("position","absolute"),t.style.setProperty("bottom","20px"),t.style.setProperty("right","140px"),t.style.setProperty("z-index","100"),t.style.setProperty("cursor","pointer"),t.style.setProperty("width","40px"),t.style.setProperty("height","40px"),t.style.setProperty("background","#666666"),t.style.setProperty("border-radius","100%");var e=document.createElement("div");return e.style.setProperty("width","10px"),e.style.setProperty("height","10px"),e.style.setProperty("border-left","2px solid #fff"),e.style.setProperty("border-bottom","2px solid #fff"),e.style.setProperty("transform","rotate(45deg)"),e.style.setProperty("position","absolute"),e.style.setProperty("top","15px"),e.style.setProperty("left","15px"),t.append(e),t},t.prototype.updatePagination=function(){var t=this.wrapper.querySelector(".pptx-preview-wrapper-pagination");t&&(t.innerText="".concat(this.currentIndex+1,"/").concat(this.slideCount))},t.prototype.renderPagination=function(t){var e=document.createElement("div");e.classList.add("pptx-preview-wrapper-pagination"),e.innerText="".concat(this.currentIndex+1,"/").concat(this.slideCount),e.style.setProperty("position","absolute"),e.style.setProperty("bottom","33px"),e.style.setProperty("right","20px"),e.style.setProperty("color","#666666"),e.style.setProperty("font-size","14px"),e.style.setProperty("z-index","100"),t.append(e)},t.prototype.removeCurrentSlide=function(){var t=this.wrapper.querySelector(".pptx-preview-slide-wrapper-".concat(this.currentIndex));t&&this.wrapper.removeChild(t),wt("removeSlide")},t.prototype.renderNextSlide=function(){this.removeCurrentSlide(),this.currentIndex=this.currentIndex<this.slideCount-1?this.currentIndex+1:0,this.htmlRender.renderSlide(this.currentIndex),this.updatePagination()},t.prototype.renderPreSlide=function(){this.removeCurrentSlide(),this.currentIndex=this.currentIndex>0?this.currentIndex-1:this.slideCount-1,this.htmlRender.renderSlide(this.currentIndex),this.updatePagination()},t.prototype._addPre=function(t){},t.prototype.preview=function(t){var e=this;return wt("destroy"),bt("destroy"),new Promise((function(a,r){e.wrapper.innerHTML="";var n=e.pptx=new Q;n.load(t).then((function(){try{var t=e.htmlRender=new mt(e.wrapper,n,{viewPort:{width:e.options.width,height:e.options.height},mode:e.options.mode});if("slide"===e.options.mode){var o=e.renderNextButton();o.onclick=function(){e.renderNextSlide()},e.wrapper.append(o);var c=e.renderPreButton();c.onclick=function(){e.renderPreSlide()},e.wrapper.append(c),e.renderPagination(e.wrapper),e._addPre(e.wrapper),e.currentIndex=0,t.renderSlide(0)}else for(var i=0;i<n.slides.length;i++)t.renderSlide(i);a(n)}catch(t){r(t)}})).catch((function(t){r(t)}))}))},t.prototype.destroy=function(){wt("destroy"),bt("destroy")},t}();function xt(t,e){return new gt(t,e)}export{xt as init};
